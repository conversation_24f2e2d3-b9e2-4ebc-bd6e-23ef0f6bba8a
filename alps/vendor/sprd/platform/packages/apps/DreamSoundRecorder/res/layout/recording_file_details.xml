<?xml version="1.0" encoding="UTF-8"?>
<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<!-- Created by Spreadst -->
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:fadingEdge="vertical"
    android:scrollbars="vertical"
    android:fitsSystemWindows="true">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="30dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="8dp"
                android:singleLine="true"
                android:text="@string/file_name"
                android:textColor="@color/file_details_text_color"
                android:textSize="15sp"/>

            <TextView
                android:id="@+id/file_name_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingRight="8dp"
                android:singleLine="false"
                android:padding="3dp"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="30dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="8dp"
                android:singleLine="true"
                android:text="@string/file_size"
                android:textColor="@color/file_details_text_color"
                android:textSize="15sp"/>

            <TextView
                android:id="@+id/file_size_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingRight="8dp"
                android:singleLine="false"
                android:padding="3dp"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="30dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="8dp"
                android:singleLine="true"
                android:text="@string/file_path"
                android:textColor="@color/file_details_text_color"
                android:textSize="15sp"/>

            <TextView
                android:id="@+id/file_path_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingRight="8dp"
                android:ellipsize="end"
                android:maxLines="6"
                android:singleLine="false"
                android:padding="3dp"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="30dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:paddingLeft="8dp"
                android:text="@string/file_date"
                android:textColor="@color/file_details_text_color"
                android:textSize="15sp"/>

            <TextView
                android:id="@+id/file_date_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingRight="8dp"
                android:singleLine="false"
                android:padding="3dp"/>
        </LinearLayout>

    </LinearLayout>
</ScrollView>