<?xml version="1.0" encoding="UTF-8"?>
<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<!-- Created by Spreadst -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/soundbackground"
                android:orientation="vertical"
                android:fitsSystemWindows="true">
    <RelativeLayout
        android:id="@+id/wavesLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/timerViewLayout"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="80dp">
    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/timerViewLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/opt_button"
        android:background="@color/main_background_color"
        android:gravity="center_horizontal"
        android:orientation="vertical">
        <TextView
            android:id="@+id/timerView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="center_horizontal"
            android:paddingBottom="1dp"
            android:textColor="@color/timer_view_color"
            android:textSize="44dp"/>

        <TextView
            android:id="@+id/record_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignStart="@id/timerView"
            android:layout_below="@id/timerView"
            android:layout_marginEnd="5dp"
            android:layout_marginTop="5dp"
            android:paddingBottom="8dp"
            android:text="@string/recording"
            android:textColor="@color/timer_view_color"
            android:textSize="14dp"/>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/opt_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/main_background_color"
    >
        <Button
            android:id="@+id/previousButton"
            android:layout_width="100dp"
            android:layout_height="90dp"
            android:layout_marginBottom="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="36dp"
            android:layout_marginTop="-5dp"
            android:background="#00000000"
            android:drawableLeft="@drawable/before_tag_disabled"
            android:drawablePadding="-5dp"
            android:text="@string/previous_tag"
            android:textColor="@color/have_more_tag_color"/>

        <ImageButton
            android:id="@+id/playButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="50dp"
            android:layout_marginTop="5dp"
            android:background="@drawable/custom_record_play_btn"
            android:src="@drawable/custom_record_play_btn"/>

        <Button
            android:id="@+id/nextButton"
            android:layout_width="100dp"
            android:layout_height="90dp"
            android:layout_alignParentEnd="true"
            android:layout_marginBottom="50dp"
            android:layout_marginStart="36dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="-5dp"
            android:background="#00000000"
            android:drawablePadding="-5dp"
            android:drawableRight="@drawable/next_tag_disabled"
            android:text="@string/next_tag"
            android:textColor="@color/have_more_tag_color"/>
    </RelativeLayout>


</RelativeLayout>