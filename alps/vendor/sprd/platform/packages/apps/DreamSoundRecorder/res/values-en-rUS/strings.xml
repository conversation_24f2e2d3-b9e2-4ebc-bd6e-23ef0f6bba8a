<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="app_name">Sound Recorder</string>
    <string name="record_your_message">Record your message</string>
    <string name="message_recorded">Message recorded</string>
    <string name="review_message">Message lyrics</string>
    <string name="recording">Recording</string>
    <string name="recording_stopped">Recording stopped</string>
    <string name="recording_save">Recording saved</string>
    <string name="recording_nosave">Recording wasn\'t saved</string>
    <string name="storage_is_full">Storage is full</string>
    <string name="max_length_reached">Maximum length reached</string>
    <string name="insert_sd_card">Insert an SD card</string>
    <string name="phone_message">Can\'t turn on Sound Recorder while calling.</string>
    <string name="min_available">About <xliff:g id="minutes">%d</xliff:g> min available</string>
    <string name="min_and_time_available">About <xliff:g id="minutes">%1$d</xliff:g> min and <xliff:g id="seconds">%2$d</xliff:g> sec available</string>
    <string name="sec_available">About <xliff:g id="seconds">%d</xliff:g>s available</string>
    <string name="accept">save</string>
    <string name="discard">Discard</string>
    <string name="button_ok">SAVE</string>
    <string name="press_record">Press record</string>
    <string name="audio_db_title_format"><xliff:g id="format">yyyy-MM-dd HH:mm:ss</xliff:g></string>
    <string name="audio_db_artist_name">Your recordings</string>
    <string name="audio_db_album_name">Audio recordings</string>
    <string name="audio_db_playlist_name">My recordings</string>
    <string name="error_sdcard_access">Unable to access SD card</string>
    <string name="error_app_internal">Internal application error</string>
    <string name="error_mediadb_new_record">Couldn\'t save recorded audio</string>
    <string name="save">Save</string>
    <string name="select_file_type">Select the record file type</string>
    <string name="record_amr">AMR (Low-quality)</string>
    <string name="record_3gpp">3GPP (High-grade)</string>
    <string name="record_aac">AAC (High-grade)</string>
    <string name="button_cancel">Cancel</string>
    <string name="path_save">Default path saved</string>
    <string name="path_nosave">Default path not saved</string>
    <string name="path_default">The current path isn\'t writable or invalid. It\'s the path to the internal storage.</string>
    <string name="menu_set_save_path">Set save path</string>
    <string name="back">Back..</string>
    <string name="path_label">Save to</string>
    <string name="pause">pause</string>
    <string name="storage_is_not_enough">Storage isn\'t enough</string>
    <string name="menu_recording_file_list">Recordings</string>
    <string name="recording_list_empty">Empty list</string>
    <string name="menu_recording_list_select_all">Select all</string>
    <string name="menu_recording_list_delete">Delete</string>
    <string name="menu_recording_list_deselect_all">Deselect</string>
    <string name="recording_file_delete_success">Recording(s) deleted</string>
    <string name="recording_file_database_failed">Couldn\'t delete database file</string>
    <string name="recording_file_delete_failed">Couldn\'t delete recording file</string>
    <string name="list_recorder_item_time_format">%1$s \n %2$s</string>
    <string name="list_recorder_item_size_format_b">%1$s\u0020B</string>
    <string name="list_recorder_item_size_format_kb">%1$s\u0020KB</string>
    <string name="list_recorder_item_size_format_mb">%1$s\u0020MB</string>
    <string name="confirm_del">Delete the files?</string>
    <string name="button_delete">Delete</string>
    <string name="recording_file_delete_alert_title">Delete</string>
    <string name="recording_file_delete_alert_message">Delete %1$s?</string>
    <string name="recording_file_list">Recordings</string>
    <string name="less_than_one_second">1 second</string>
    <string name="is_scanning">Media scanner is scanning now, please wait…</string>
    <string name="emptylist">List is empty</string>
    <string name="choose_file">Select the recording you want to delete files.</string>
    <string name="error_path">The path you selected doesn\'t exist. Set again.</string>
    <string name="file_does_not_exist">The file being played doesn\'t exist</string>
    <string name="path_miss">The stored path doesn\'t exist or can\'t be accessed.</string>
    <string name="file_is_corrupt">File is corrupt</string>
    <string name="low_memory">Memory isn\'t enough. Recording will stop.</string>
    <string name="record_error">Recording an exception occurs.</string>
    <string name="play_error">Music player has been disabled. Couldn\'t open.</string>
    <string name="dialog_title">Please select</string>
    <string name="dialog_message">Save the recording?</string>
    <string name="same_application_running">The recording device is being used by another program. Close this program.</string>
    <string name="recording_time_short">"Recording time less than 1 second, not saved"</string>
    <string name="file_path_button">File path</string>
    <string name="file_name">File name: </string>
    <string name="file_path">File path: </string>
    <string name="rename">Rename</string>
    <string name="rename_save">Rename completed</string>
    <string name="rename_nosave">Couldn\'t rename</string>
    <string name="filename_is_not_modified">File name isn\'t modified</string>
    <string name="duplicate_name">File name duplicate</string>
    <string name="soundpicker_label">Select recording file</string>
    <string name="error_sdcard_path">SD card doesn\'t exist. Default save path is changed to internal storage.</string>
    <string name="input_length_overstep">Input reached the max length</string>
    <string name="filename_empty_error">File name can\'t be empty. Save the original name.</string>
    <string name="please_choose_files">Choose files</string>
    <string name="selected_files_count"> has selected <xliff:g id="COUNTS">%d</xliff:g></string>
    <string name="use_default_path">The stored path doesn\'t exist. Use default path.</string>
    <string name="stroage_not_mounted">The storage isn\'t mounted</string>
    <string name="special_char_exist">Special characters <xliff:g id="COUNTS">%s</xliff:g> are unsupported</string>
    <string name="menu_select_more">Select more</string>
    <string name="menu_select_type">Set file type</string>
    <string name="path_miss_nosave">The stored path doesn\'t exist. Recording wasn\'t saved.</string>
    <string name="dialog_dismiss">Dismiss</string>
    <string name="error_permissions">The app doesn\'t have critical permissions needed to run. Check your permissions settings.</string>
    <string name="internal_sdcard">Internal SD card</string>
    <string name="external_sdcard">External SD card</string>
    <string name="internal_storage">Phone</string>
    <string name="external_storage">SD card</string>
    <string name="set_as_ring">Set as ringtone</string>
    <string name="read_file_path">Check the file path</string>
    <string name="operate_share">Share</string>
    <string name="ringtone_title_sim1">SIM1 phone ringtone</string>
    <string name="ringtone_title_sim2">SIM2 phone ringtone</string>
    <string name="ringtone_menu_short">Use as ringtone</string>
    <string name="please_insert_sim_card">Insert a SIM card at least</string>
    <string name="ring_set_silent_vibrate">Custom ringtones is unsupported in the current mode</string>
    <string name="ringtone_set_sim1">\"<xliff:g example="Alarm Bell" id="name">%s</xliff:g>\" set as SIM1 phone ringtone.</string>
    <string name="ringtone_set_sim2">\"<xliff:g example="Alarm Bell" id="name">%s</xliff:g>\" set as SIM2 phone ringtone.</string>
    <string name="ringtone_set">\"<xliff:g example="Alarm Bell" id="name">%s</xliff:g>\" set as phone ringtone.</string>
    <string name="ring_set_fail">The song couldn\'t be played. Select another song as ringtone.</string>
    <string name="tag_limit">The number of tags has reached the maximum</string>
    <string name="no_allow_play_calling">Can\'t play any audio file during call.</string>
    <string name="playback_failed">The player doesn\'t support this type of audio file.</string>
    <string name="tag">Tag</string>
    <string name="previous_tag">Last tag</string>
    <string name="next_tag">Next tag</string>
    <string name="recording_play">Recording play</string>
    <string name="clip_duration_tip">Edit length</string>
    <string name="record_setting">Settings</string>
    <string name="file_type">Format</string>
    <string name="type_amr">AMR</string>
    <string name="type_3gpp">3GPP</string>
    <string name="save_path">Save location</string>
    <string name="set_timer_recording">Scheduled recording</string>
    <string name="timer_recording_detail">Start record at <xliff:g id="hours">%1$s</xliff:g>:<xliff:g id="minutes">%2$s</xliff:g> for <xliff:g id="COUNTS">%3$s</xliff:g></string>
    <string name="view_file_details">View details</string>
    <string name="file_size">File size: </string>
    <string name="file_date">File date: </string>
    <string name="recording_start_time">Recording start time</string>
    <string name="recording_duration">Recording duration hour</string>
    <string name="tuned_timer_recording">Timer recording turned on</string>
    <string name="miss_timer_recording">Missed timer recording</string>
    <string name="miss_timer_time">Time: <xliff:g id="TIME">%s</xliff:g></string>
    <string name="duration_half_hour">0</string>
    <string name="duration_one_hour">1</string>
    <string name="duration_two_hour">2</string>
    <string name="duration_three_hour">3</string>
    <string-array name="duration_hours">
        <item> hour</item>
        <item> hours</item>
    </string-array>
    <string-array name="timer_recording_time">
        <item>Timer recording set for less than 1 minute from now.</item>
        <item>Timer recording set for <xliff:g id="HOURS">%1$s</xliff:g> from now.</item>
        <item>Timer recording set for <xliff:g id="MINUTES">%2$s</xliff:g> from now.</item>
        <item>Timer recording set for <xliff:g id="HOURS">%1$s</xliff:g> and <xliff:g id="MINUTES">%2$s</xliff:g> from now.</item>
    </string-array>
    <plurals name="hours">
        <item quantity="one">1 hour</item>
        <item quantity="other"><xliff:g id="number">%s</xliff:g> hours</item>
    </plurals>
    <plurals name="minutes">
        <item quantity="one">1 minute</item>
        <item quantity="other"><xliff:g id="number">%s</xliff:g> minutes</item>
    </plurals>
    <string name="start_record">start record</string>
    <string name="resume_record">resume record</string>
    <string name="start_play">start play</string>
    <string name="resume_play">resume play</string>
    <string name="reset_timer_recording">The recording device is use by another program. Reset timer recording.</string>
    <string name="storage_not_enough">Storage isn\'t enough. Couldn\'t start recording.</string>
    <string name="phone_storage_not_enough">Phone storage isn\'t enough.</string>
    <string name="illegal_chars_of_filename">File name can\'t contain illegal characters \\:*?\"\u003C\u003E|/</string>
    <string name="hour">H</string>
    <string name="min">M</string>
    <string name="start">start</string>
    <string name="record">rec</string>
    <string name="timeset">Timer set</string>
    <string name="starttimeandduration">Start time &amp; duration</string>
    <string name="local_record">Local record</string>
    <string name="call_record">Call record</string>
    <string name="clip_time">Clip duration</string>
    <string name="clip_name_suffix">Clip</string>
    <string name="file_choose">File choose</string>
    <string name="save_dialog_title">New recording</string>
    <string name="keep_original_file">Keep original file</string>
    <string name="save_successful">Saved. Save path: <xliff:g id="FILEPATH">%1$s</xliff:g></string>
    <string name="save_failed">Couldn\'t save.</string>
    <string name="recording_now">Recording...</string>
    <string name="recording_pause">Recording paused...</string>
    <string name="record_pause">Pause</string>
    <string name="record_stop">Stop</string>
    <string name="record_stop_and_save">STOP AND SAVE</string>
    <string name="record_timer">Timer record</string>
    <string name="record_miss">Missed timer record</string>
    <string name="close_timer">Close timer record</string>
    <string name="soundrecording">Recording</string>
    <string name="pauserecording">Recording is paused</string>
    <string name="renamenewfile">Rename new record file</string>
    <string name="clip_file_exit">The file name is repeated. Re-enter it.</string>
    <string name="autosaverecord">Auto save</string>
    <string name="auto_save">Automatically save recordings</string>
    <string name="new_record">New record</string>
    <string name="speak_open">Receiver mode is on</string>
    <string name="speak_close">Receiver mode is off</string>
    <string name="hour_duration"><xliff:g id="hour">%s</xliff:g>H</string>
    <string name="button_set">set</string>
    <string name="button_tag">tag</string>
    <string name="button_list">list</string>
    <string name="type_mp3">MP3</string>
    <string name="record_mp3">MP3 (High-quality)</string>
    <string name="file_rename">The file name is repeat. It\'s not saved.</string>
    <string name="background_speak_close">The record play can\'t work in the background and is closed</string>
    <plurals name="confirm_delfile">
        <item quantity="one">Delete the file?</item>
        <item quantity="other">Delete the files?</item>
    </plurals>
    <string name="save_power">Save power mode</string>
    <string name="save_power_summary">Not save wave data in save power mode</string>
    <string name="mode_change">The audio mode change need time. Please wait…</string>
    <string name="rename_button">ABANDON</string>
    <string name="no_sd_write_permission">Sound Recorder doesn\'t have access to your SD card. Authorize or clear the storage of the application and operate again.</string>
    <string name="confirm">OK</string>
    <string name="superuser_request_confirm">Sound Recorder doesn\'t have access to your SD card. Select the external root directory SANDISK SD card and authorize Sound Recorder to operate.</string>
    <string name="exit_multiwindow_tips">Sound Recorder doesn\'t support multi-window mode. Use Sound Recorder in full screen mode.</string>
    <string name="share_max_warning">More than 100 files were selected, and share function was unavailable.</string>
    <string name="storage_not_enough_save_wave_tag">The recording has been saved, and the internal storage of the phone is insufficient to save the waveform and tag.</string>
    <string name="ram_not_enough_load_wave">The RAM is insufficient and the wave data can\'t be loaded</string>
    <string name="document_request_confirm">Sound Recorder doesn\'t have access to use the directory you choose. Select the &lt;Android&gt; directory and authorize Sound Recorder to operate.</string>
    <string name="ring_null_set_fail">Couldn\'t set ringtone.</string>
</resources>