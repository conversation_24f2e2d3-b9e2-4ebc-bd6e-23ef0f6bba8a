<?xml version="1.0" encoding="UTF-8"?>
<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<!-- Created by Spreadst -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:id="@+id/record_item_container"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:descendantFocusability="blocksDescendants"
              android:background="@color/main_background_color"
              android:baselineAligned="false"
              android:orientation="vertical"
              android:fitsSystemWindows="true" >

    <LinearLayout
        android:id="@+id/record_item"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_alignParentLeft="true"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/record_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="13dp"
            android:layout_marginRight="13dp"
            android:layout_gravity="center_vertical"
            android:orientation="vertical">
            <ImageView
                android:id="@+id/recode_icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:background="@drawable/custom_record_listrecord_btn"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/middle_display"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="vertical" >
            <TextView
                android:id="@+id/record_displayname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:ellipsize="middle"
                android:layout_marginBottom="3dp"
                android:singleLine="true"
                android:textColor="@color/record_displayname_color"
                android:textSize="16sp"/>
            <TextView
                android:id="@+id/record_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="#757575"
                android:textSize="10sp"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:layout_marginRight="15dp" >
            <ImageView
                android:id="@+id/tag_icon"
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="10dp"
                android:src="@drawable/tag_list"/>

            <TextView
                android:id="@+id/record_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_gravity="center_vertical"
                android:singleLine="true"
                android:textColor="#757575"
                android:textSize="16dp"/>
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/play_layout"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_alignParentLeft="true"
        android:orientation="horizontal"
        android:layoutDirection="ltr">
        <TextView
            android:id="@+id/current_time"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_marginLeft="13dp"
            android:layout_marginTop="0dp"
            android:layout_weight="4"
            android:maxLines="1"
            android:textColor="#a4a4a4"
            android:textSize="9dp"/>
        <com.sprd.soundrecorder.ui.MarkSeekBar
            android:id="@+id/progress"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_marginLeft="0dp"
            android:layout_marginRight="0dp"
            android:layout_marginTop="0dp"
            android:layout_weight="20"
            android:background="@android:color/transparent"
            android:ellipsize="middle"
            android:maxHeight="2dp"
            android:minHeight="2dp"
            android:paddingBottom="4dp"
            android:paddingLeft="0dp"
            android:paddingRight="0dp"
            android:progressDrawable="@drawable/progress_horizontal_new"
            android:thumb="@drawable/seek_thumb"/>
        <TextView
            android:id="@+id/total_duration"
            android:layout_weight="4"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:maxLines="1"
            android:textColor="#a4a4a4"
            android:textSize="9dp"/>
    </LinearLayout>
</LinearLayout>
