<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources xmlns:android="http://schemas.android.com/apk/res/android">
    <style name="Theme.SoundRecorder" parent="@android:style/Theme.Material.Light.DarkActionBar">
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="android:statusBarColor">@color/status_bar_color_blue</item>
        <item name="android:actionModeStyle">@style/Holo.FilePlayActionBar</item>
        <item name="android:actionBarStyle">@style/Holo.FilePlayActionBar</item>
        <item name="android:actionBarSize">56dip</item>
        <item name="android:actionMenuTextAppearance">@style/MenuTextStyle</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement">true</item>
    </style>
    <style name="Theme.RecordingFileList" parent="@android:style/Theme.Material.Light.DarkActionBar">
        <item name="android:actionModeCloseDrawable">@drawable/ic_ab_back_holo_dark</item>
        <item name="android:actionModeStyle">@style/Holo.ActionMode</item>
        <item name="android:actionBarStyle">@style/Holo.ActionBar</item>
        <item name="android:actionBarSize">56dip</item>
        <item name="android:actionMenuTextAppearance">@style/MenuTextStyle</item>
        <item name="android:statusBarColor">@color/status_bar_color_blue</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement">true</item>
    </style>
    <style name="Theme.SoundPicker" parent="@android:style/Theme.Material.Light.DarkActionBar">
        <item name="android:actionModeCloseDrawable">@drawable/ic_ab_back_holo_dark</item>
        <item name="android:actionBarStyle">@style/Holo.ActionBar</item>
        <item name="android:actionBarSize">56dip</item>
        <item name="android:actionMenuTextAppearance">@style/MenuTextStyle</item>
        <item name="android:statusBarColor">@color/status_bar_color_blue</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement">true</item>
    </style>
    <style name="Holo.ActionBar" parent="@android:style/Widget.Material.ActionBar">
        <item name="android:background">@color/status_bar_color_blue</item>
        <item name="android:titleTextStyle">@style/AcBar_titleStyle</item>
    </style>
    <style name="Holo.FilePlayActionBar" parent="@android:style/Widget.Material.ActionBar">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:titleTextStyle">@style/AcBar_titleStyle</item>
    </style>
    <style name="AcBar_titleStyle">
        <item name="android:textSize">18dp</item>
        <item name="android:textColor">#FFFFFF</item>
    </style>
    <style name="MenuTextStyle">
        <item name="android:textColor">?android:attr/actionMenuTextColor</item>
        <item name="android:textSize">18dp</item>
    </style>
    <style name="Holo.ActionMode" parent="@android:style/Widget.Material.ActionBar">
        <item name="android:background">@color/menu_color_gray</item>
        <item name="android:titleTextStyle">@style/AcBar_titleStyle</item>
    </style>
    <style name="AppTheme.Base" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement">true</item>
    </style>
    <style name="AppTheme" parent="AppTheme.Base">
        <item name="android:actionOverflowMenuStyle">@style/OverflowMenuStyle</item>
        <item name="android:toolbarStyle">@style/CustomToolbar</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="windowActionBarOverlay">true</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="android:statusBarColor">@color/status_bar_color_blue</item>
    </style>
    <style name="OverflowMenuStyle" parent="Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:paddingRight">3dp</item>
    </style>
    <style name="CustomToolbar" parent="Widget.AppCompat.Toolbar">
        <item name="contentInsetStart">0dp</item>
    </style>
</resources>
