<?xml version="1.0" encoding="utf-8"?>
<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<!-- Created by Spreadst -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="horizontal"
    android:fitsSystemWindows="true" >
    
    <NumberPicker android:id="@+id/np1"
        android:layout_width="40dp"
        android:layout_marginLeft="5dp"
        android:descendantFocusability="blocksDescendants"
        android:layout_height="wrap_content"/>

   <TextView
        android:id="@+id/hour"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:text="@string/hour"
        android:textColor="@color/timer_view_color"
        android:textSize="15dp" />


    <NumberPicker android:id="@+id/np2"
        android:layout_width="40dp"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"/>

   <TextView
        android:id="@+id/min"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/min"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="5dp"
        android:textColor="@color/timer_view_color"
        android:textSize="15dp" />
        <TextView
        android:id="@+id/start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:text="@string/start"
        android:layout_marginRight="5dp"
        android:textColor="@color/timer_view_color"
        android:textSize="15dp" />
        <!--
       <TextView
        android:id="@+id/record"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/record"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="5dp"
        android:textColor="#4b4c57"
        android:textSize="15dp" />
        -->
    <NumberPicker android:id="@+id/np3"
        android:layout_width="65dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="5dp"
        android:descendantFocusability="blocksDescendants"/>
</LinearLayout>