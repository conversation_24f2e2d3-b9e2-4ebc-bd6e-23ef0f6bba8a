<?xml version="1.0" encoding="utf-8"?>
<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/main_background_color"
    android:orientation="vertical"
    android:fitsSystemWindows="true" >

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/layout_app_bar"
        android:layout_width="match_parent"
        android:background="@color/status_bar_color_blue"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true" >

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/id_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:navigationIcon ="@drawable/before_tag_disabled"
            android:title="@string/recording_file_list"
            android:gravity="center_vertical"
            android:titleTextColor="@android:color/white"
            android:background="@color/status_bar_color_blue"
            android:fitsSystemWindows="true">
        </androidx.appcompat.widget.Toolbar>

        <com.sprd.soundrecorder.SlidingTabLayout
            android:id="@+id/tabs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/status_bar_color_blue" />
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/id_page_vp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" >
    </androidx.viewpager.widget.ViewPager>
</LinearLayout>

