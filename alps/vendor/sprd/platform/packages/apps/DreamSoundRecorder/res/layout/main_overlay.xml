<?xml version="1.0" encoding="UTF-8"?>
<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<!-- Created by Spreadst -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/soundbackground"
                android:fitsSystemWindows="true"
                android:orientation="vertical">
    <TextView
        android:id="@+id/stateMessage2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:gravity="center_horizontal"
        android:text="@string/recording"
        android:textColor="#ffffff"
        android:textSize="20sp"/>
    <RelativeLayout
        android:id="@+id/wavesLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/record_time_show"
        android:layout_below="@id/stateMessage2"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp">
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/record_time_show"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/opt_button"
        android:background="@color/main_background_color"
        android:gravity="center_horizontal"
        android:orientation="vertical">
        <TextView
            android:id="@+id/timerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="center_horizontal"
            android:paddingBottom="1dp"
            android:textColor="@color/timer_view_color"
            android:textSize="44dp"/>

        <TextView
            android:id="@+id/filename"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/timerView"
            android:layout_marginTop="5dp"
            android:gravity="center_horizontal"
            android:paddingBottom="18dp"
            android:textColor="#b0b0b0"
            android:textSize="16dp"/>
    </RelativeLayout>
    <LinearLayout
        android:id="@+id/opt_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/main_background_color"
        android:gravity="center_horizontal"
        android:fitsSystemWindows="true">
        <Button
            android:id="@+id/tagButton"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="36dp"
            android:layout_marginTop="18dip"
            android:layout_marginBottom="50dp"
            android:background="@drawable/custom_record_set_btn"
            android:layout_gravity="center_vertical"
            android:paddingBottom="3dp"
            android:textColor="#ffffff"
            android:textSize="10dp"
        />

        <ImageButton
            android:id="@+id/recordButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="50dp"
            android:layout_marginTop="18dip"
            android:layout_gravity="center_vertical"
            android:background="@drawable/custom_record_btn"/>

        <Button
            android:id="@+id/stopButton"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:layout_marginBottom="50dp"
            android:layout_marginLeft="36dp"
            android:layout_marginRight="20dp"
            android:layout_marginTop="18dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/custom_record_list_btn"/>
    </LinearLayout>
</RelativeLayout>