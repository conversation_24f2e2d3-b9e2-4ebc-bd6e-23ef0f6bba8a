<?xml version="1.0" encoding="utf-8"?>
<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true" >

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:textSize="18dp"
        android:text="@string/save_dialog_title"
        android:layout_marginTop="20dp"/>

    <EditText
        android:id="@+id/inputname"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title"
        android:layout_marginTop="8dp"
        android:inputType="textFilter"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_below="@+id/inputname"
        android:background="@android:color/transparent"
        android:padding="8dip" >

        <Button
            android:id="@+id/button_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_gravity="center_horizontal"
            android:layout_marginRight="8dip"
            android:background="?android:attr/selectableItemBackground"
            android:text="@string/button_ok"
            android:textColor="@color/dialog_text_button_color" />

        <Button
            android:id="@+id/button_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toLeftOf="@id/button_ok"
            android:background="?android:attr/selectableItemBackground"
            android:text="@string/button_cancel"
            android:textColor="@color/dialog_text_button_color" />
    </RelativeLayout>

</RelativeLayout>
