<?xml version="1.0" encoding="UTF-8"?>
<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<!-- Created by Spreadst -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/record_item_container"
    android:layout_width="fill_parent"
    android:layout_height="match_parent"
    android:baselineAligned="false"
    android:orientation="horizontal"
    android:fitsSystemWindows="true" >

    <LinearLayout
        android:id="@+id/record_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        android:layout_marginLeft="13dp"
        android:layout_marginRight="13dp"
        android:layout_gravity="center_vertical"
        android:orientation="vertical"
        android:clickable="false" >

        <CheckBox
            android:id="@+id/recode_checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/checkbox_style"
            android:checked="false"
            android:clickable="false"
            android:focusable="false"
            android:gravity="center_vertical"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical" >
            <TextView
                android:id="@+id/record_displayname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:ellipsize="middle"
                android:layout_marginBottom="3dp"
                android:singleLine="true"
                android:textColor="@color/record_displayname_color"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/record_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="#7d7d7d"
                android:textSize="10sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:paddingRight="15dp" >

        <ImageView
            android:id="@+id/tag_icon"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_marginLeft="10dp"
            android:layout_gravity="center_vertical"/>

        <TextView
            android:id="@+id/record_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_gravity="center_vertical"
            android:singleLine="true"
            android:textColor="#afafaf"
            android:textSize="16sp" />
    </LinearLayout>

</LinearLayout>