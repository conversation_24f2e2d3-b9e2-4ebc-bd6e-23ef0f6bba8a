<?xml version="1.0" encoding="utf-8"?>
<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:fitsSystemWindows="true"
    android:background="@color/main_background_color">
<LinearLayout
        android:layout_width="match_parent"
        android:background="@color/status_bar_color_blue"
        android:layout_height="wrap_content">
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/id_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/status_bar_color_blue"
        android:titleTextColor="@color/white">
        <TextView
        android:id="@+id/tv_toolbar"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold"
        android:text="@string/recording"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    </androidx.appcompat.widget.Toolbar>
    </LinearLayout>
    <ListView
        android:id="@android:id/list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:drawSelectorOnTop="false"
        android:dividerHeight="1px"
        android:fadingEdgeLength="16dip"
        android:scrollbarStyle="insideOverlay"
        android:background="@color/main_background_color"/>
        <TextView
        android:id="@+id/emptylist"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:gravity="center"
        android:text="@string/emptylist"
        android:textColor="@color/text_color_empty_list"
        android:textSize="@dimen/text_size_empty_list"
        android:visibility="gone"
        android:textAppearance="?android:attr/textAppearanceMedium"/>

</LinearLayout>
