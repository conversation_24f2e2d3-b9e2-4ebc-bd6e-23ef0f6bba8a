Component: DreamSoundRecorder

Copyright 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd.

This component was developed by Unisoc (Shanghai) Technologies Co., Ltd. In 2016, and distributed under the terms of Unisoc General Software License.

Some content developed by others have been included in this component, and unless otherwise noted in the body of the certain file(s), the following copyright notices will apply to vendor/sprd/platform/packages/apps/DreamSoundRecorder:

(1)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/.gradle/4.1/fileHashes/fileHashes.lock
NOASSERTION
NOASSERTION
(2)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/.idea/codeStyles/Project.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(3)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/.idea/gradle.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(4)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/.idea/misc.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(5)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/.idea/modules.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(6)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/.idea/vcs.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(7)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/.idea/workspace.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(8)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/Android.bp
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0 android_app { name: "DreamSoundRecorder",
LicenseRef-Unisoc-General-1.0
(9)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/CleanSpec.mk
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(10)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/DreamSoundRecorder.iml
NOASSERTION
NOASSERTION
(11)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/INFO
NOASSERTION
NOASSERTION
(12)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/UNISOC_TEST_MAPPING
NOASSERTION
NOASSERTION
(13)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/app/.gitignore
NOASSERTION
NOASSERTION
(14)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/app/AndroidManifest.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(15)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/app/build.gradle
NOASSERTION
NOASSERTION
(16)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/app/proguard-rules.pro
NOASSERTION
NOASSERTION
(17)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/build.gradle
NOASSERTION
NOASSERTION
(18)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/gradle.properties
NOASSERTION
NOASSERTION
(19)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/gradle/wrapper/gradle-wrapper.properties
NOASSERTION
NOASSERTION
(20)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/gradlew
NOASSERTION
NOASSERTION
(21)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/gradlew.bat
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd rem SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0 if "%DEBUG%" == "" @echo off rem ########################################################################## rem rem Gradle startup script for Windows rem rem ##########################################################################
LicenseRef-Unisoc-General-1.0
(22)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/jacoco-report.gradle
NOASSERTION
NOASSERTION
(23)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/local.properties
NOASSERTION
NOASSERTION
(24)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/platform.keystore
NOASSERTION
NOASSERTION
(25)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable-hdpi/Thumbs.db
NOASSERTION
NOASSERTION
(26)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable-mdpi/Thumbs.db
NOASSERTION
NOASSERTION
(27)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable-sw768dp/Thumbs.db
NOASSERTION
NOASSERTION
(28)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable-xhdpi/Thumbs.db
NOASSERTION
NOASSERTION
(29)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable-xxhdpi/Thumbs.db
NOASSERTION
NOASSERTION
(30)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable-xxxhdpi/Thumbs.db
NOASSERTION
NOASSERTION
(31)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/checkbox_style.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(32)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/custom_record_btn.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(33)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/custom_record_list_btn.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(34)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/custom_record_listpause_btn.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(35)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/custom_record_listrecord_btn.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(36)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/custom_record_play_btn.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(37)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/custom_record_set_btn.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(38)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/custom_record_stop_btn.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(39)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/custom_record_suspend_btn.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(40)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/custom_record_tag_btn.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(41)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/progress_horizontal_new.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(42)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/drawable/seek_thumb.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(43)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/activity_main.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(44)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/custom_tab.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(45)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/main_overlay.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(46)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/multi_choice_list_new.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(47)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/recording_file_details.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(48)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/recording_file_item_choice.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(49)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/recording_file_item_new.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(50)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/recording_file_list.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(51)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/recording_file_play_new.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(52)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/rename_save_dialog.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(53)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/setting_layout.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(54)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/sound_picker.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(55)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/soundpicker_file_item.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(56)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/layout/time_picker_dialog.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(57)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/menu/clip_menu.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(58)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/menu/mutli_choice.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(59)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/menu/options_menu_overlay.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(60)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/mipmap-anydpi-v26/ic_launcher.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(61)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/mipmap-hdpi/Thumbs.db
NOASSERTION
NOASSERTION
(62)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/mipmap-mdpi/Thumbs.db
NOASSERTION
NOASSERTION
(63)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/mipmap-xhdpi/Thumbs.db
NOASSERTION
NOASSERTION
(64)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/mipmap-xxhdpi/Thumbs.db
NOASSERTION
NOASSERTION
(65)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/mipmap-xxxhdpi/Thumbs.db
NOASSERTION
NOASSERTION
(66)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-af-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(67)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-af-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(68)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-af/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(69)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-am-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(70)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-am-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(71)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-am/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(72)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ar-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(73)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ar-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(74)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ar/strings.xml
© SANDISK SD الخارجية والسماح لتطبيق SoundRecorder بالعمل."</string>
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(75)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-as/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(76)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-az-rAZ/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(77)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-be-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(78)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-be-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(79)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-be/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(80)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bg-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(81)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bg-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(82)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bg/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(83)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bn-keysexposed/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(84)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bn-keyshidden/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(85)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bn-rBD/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(86)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bn/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(87)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bo-keysexposed/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(88)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bo-keyshidden/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(89)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bo/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(90)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-brx-rIN/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(91)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-bs-rBA/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(92)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ca-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(93)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ca-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(94)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ca/strings.xml
© els permisos importants necessaris per executar-se. Comprova la configuració dels permisos."</string> string name="internal_sdcard">"Deixa que ho decideixi el sistema"</string> string name="external_sdcard">"Targeta SD externa"</string> string name="internal_storage">"Emmagatzematge intern"</string> string name="external_storage">"Emmagatzematge extern"</string> string name="set_as_ring">"Establir com a to de trucada"</string> string name="read_file_path">"Comprovar el camí del fitxer"</string> string name="operate_share">"Comparteix"</string> string name="ringtone_title_sim1">"So de trucada del telèfon SIM1"</string> string name="ringtone_title_sim2">"To de trucada de la targeta SIM2"</string> string name="ringtone_menu_short">"Utilitza com a so"</string> string name="please_insert_sim_card">"Inseriu com a mínim una targeta SIM"</string> string name="ring_set_silent_vibrate">"Els sons de trucada personalitza
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
© accés per utilitzar el directori que heu triat, per favor selecciona el &lt;Android&gt; directori i autoritza la gravadora de so a funcionar."</string> string name="record_aac">"AAC (alt grau)"</string>
© accés a la teva targeta SD. Selecciona el directori arrel extern de la targeta SANDISK SD i autoritza SoundRecorder a fer-hi canvis."</string>
© accés a la teva targeta SD. Autoritza’n l’accés o esborra l’emmagatzematge de l’aplicació i torna-ho a provar."</string> string name="confirm">"D\'acord"</string>
Apache-2.0
(95)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ce/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(96)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ceb/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(97)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-cs-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(98)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-cs-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(99)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-cs/strings.xml
© záznamy"</string> string name="error_sdcard_access" product="default" msgid="5750308258096153274">"Nelze získat přístup ke kartě SD."</string> string name="error_app_internal" msgid="312884382388702022">"Vnitřní chyba aplikace."</string> string name="error_mediadb_new_record" msgid="261714902333432462">"Zaznamenaný zvuk nelze uložit."</string> string name="recording_save">"Nahrávka byla uložena"</string> string name="recording_nosave">"Nahrávání se neuložilo"</string>
© záznamy"</string>
© zapnout Nahrávač zvuku během volání."</string> string name="min_and_time_available">"Asi <xliff:g id="minutes">%1$d</xliff:g> min a <xliff:g id="seconds">%2$d</xliff:g> vteřin dostupních"</string> string name="press_record">"Stisknout nahrávání"</string> string name="save">Uložit</string>
© vyzváněcí tóny nejsou podporovány v současném režimu"</string> string name="ringtone_set_sim1">"\"<xliff:g id="name" example="Alarm Bell">%s</xliff:g>\" nastaven jako vyzváněcí tón telefonu SIM1."</string> string name="ringtone_set_sim2">"\"<xliff:g id="name" example="Alarm Bell">%s</xliff:g>\" nastaven jako vyzváněcí tón telefonu SIM2."</string> string name="ringtone_set">"Skladba <xliff:g id="NAME">%s</xliff:g> byla nastavena jako vyzváněcí tón."</string>
© přehrát. Prosím, zvolte jinou píseň jako vyzváněcí tón"</string> string name="tag_limit">"Počet tagů dosáhl maxima"</string> string name="no_allow_play_calling">"Při hovoru není povoleno přehrávat hudbu (radio/video)."</string> string name="playback_failed">"Přehrávač nepodporuje tento typ audio souboru."</string> string name="tag">"Štítek"</string> string name="previous_tag">"Poslední tag"</string> string name="next_tag">"Další tag"</string> string name="recording_play">"Nahrávání přehrávání"</string>
© nahrávání zmeškáno"</string> string name="miss_timer_time">"Čas: <xliff:g id="TIME">%s</xliff:g>"</string> string-array name="duration_hours"> item>" hodina"</item> item>" hodin"</item> string-array> string-array name="timer_recording_time">
© nahrávání!"</string> string name="phone_storage_not_enough">"Nedostatečná paměť telefonu!"</string> resources>
© nahrávání!"</string>
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(100)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-da-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(101)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-da-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(102)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-da/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(103)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-de-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(104)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-de-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(105)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-de/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(106)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-doi-rIN/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(107)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-el-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(108)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-el-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(109)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-el/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(110)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-en-rAU/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(111)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-en-rGB-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(112)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-en-rGB-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(113)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-en-rGB/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(114)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-en-rIN/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(115)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-es-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(116)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-es-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(117)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-es-rES/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(118)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-es-rUS-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(119)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-es-rUS-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(120)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-es-rUS/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(121)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-es/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
© autorización o limpie el almacenamiento de la aplicación y vuelva a intentarlo."</string> string name="no_allow_play_calling">"Cannot play any audio file during call."</string> string name="next_tag">"Próxima etiqueta"</string> string name="new_record">"Registro nuevo"</string> string name="mode_change">"Es necesario cambiar de modo de audio; por favor, espere..."</string> string name="miss_timer_time">"Hora: <xliff:g id="TIME">%s</xliff:g>"</string> string name="miss_timer_recording">"Grabación de temporizador perdida"</string> string name="min">"m"</string> string name="local_record">"Registro local"</string> string name="keep_original_file">"Mantener archivo original"</string>
Apache-2.0
(122)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-et-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(123)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-et-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(124)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-et-rEE/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(125)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-et/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(126)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-eu-rES/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(127)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-eu/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(128)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-fa-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(129)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-fa-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(130)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-fa/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(131)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-fi-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(132)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-fi-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(133)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-fi/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(134)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-fr-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(135)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-fr-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(136)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-fr-rCA/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(137)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-fr/strings.xml
© sélectionnés, et la fonction de partage n\'était pas disponible"</string>
© prise en compte"</string> string name="file_path_button">"Chemin du fichier"</string> string name="file_name">"Nom du fichier:"</string> string name="file_path">"Chemin du fichier:"</string> string name="rename">"Renommer"</string>
© pour <xliff:g id="MINUTES">%2$s</xliff:g> à partir de maintenant."</item>
© pour <xliff:g id="HOURS">%1$s</xliff:g> et <xliff:g id="MINUTES">%2$s</xliff:g> à partir de maintenant."</item> string-array>
© pour <xliff:g id="HOURS">%1$s</xliff:g> à partir de maintenant."</item>
© pour moins de 1 minute à partir de maintenant."</item>
© par un autre programme, veuillez réinitialiser l\'enregistrement par minuterie!"</string>
© par un autre programme, s\'il vous plaît, veuillez fermer le programme!"</string>
© n\'existe pas, utiliser le chemin par défaut"</string>
© n\'existe pas, s\'il vous plaît veuillez redéfinir!"</string>
© n\'existe pas ou ne peut pas être accessible!"</string>
© n\'existe pas, l\'enregistrement n\'a pas été sauvegardé!"</string>
© enregistré et la mémoire interne du téléphone est insuffisante pour enregistrer la forme d'onde et l'étiquette"</string>
© désactivé, impossible de l\'ouvrir!"</string>
© défini comme sonnerie."</string>
© dans la mémoire interne."</string>
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
© avec succès"</string> string name="rename_nosave">"Échec du changement de nom"</string>
Apache-2.0
(138)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-gl-rES/strings.xml
© suficiente</string> string name="menu_recording_file_list">Rexistrando lista de arquivos</string> string name="recording_list_empty">Lista baleira</string> string name="menu_recording_list_select_all">Seleccionar todo</string> string name="menu_recording_list_delete">Borrar</string> string name="menu_recording_list_deselect_all">Deseleccione</string> string name="recording_file_delete_success">Rexistrando a eliminación</string> string name="recording_file_database_failed">Erro ó borrar o arquivo da base de datos</string> string name="recording_file_delete_failed">Erro ó borrar o arquivo o arquivo de rexistro</string> string name="confirm_del">¿Está seguro que desexa borrar os arquivos?</string> string name="button_delete">Borrar</string> string name="recording_file_delete_alert_title">Borrar</string> string name="recording_file_delete_alert_message">¿Seguro de borrar %1$s?</string> string name="reco
© suficiente!"</string>
© suficiente, erro no rexistro do temporizador!"</string>
© sobrescribible ou inválida, a súa ruta á ruta de almacenamento interno</string> string name="menu_set_save_path">Axustar ruta de memorización</string> string name="back">Volver...</string> string name="path_label">Seleccione a ruta para gardar</string> string name="pause">Pausa</string>
© insuficiente, deterase o rexistro!</string> string name="record_error">"¡Rexistrando porque ocorreu unha excepción!"</string> string name="play_error">¡Deshabilouse o reprodutor de música, incapaz de abrir！</string> string name="dialog_title">Seleccionar</string> string name="dialog_message">"¿Desexa gardar o rexistro?"</string> string name="same_application_running">"O dispositivo de gravación está a ser usado por outro programa, peche este programa"</string>
© inferior a 1 minutos desde ágora."</item> item>"Axuste da grabación do temporizador para <xliff:g id="HOURS">%1$s</xliff:g> dende ágora."</item> item>"Axuste da grabación do temporizador para <xliff:g id="MINUTES">%2$s</xliff:g> dende ágora."</item> item>"Axuste da grabación do temporizador para <xliff:g id="HOURS">%1$s</xliff:g> e <xliff:g id="MINUTES">%2$s</xliff:g> dende ágora."</item> string-array> plurals name="hours"> item quantity="one">1 hora</item> item quantity="other">"<xliff:g id="number">%s</xliff:g> horas"</item> plurals> plurals name="minutes"> item quantity="one">1 minuto</item> item quantity="other">"<xliff:g id="number">%s</xliff:g> minutos"</item> plurals> string name="start_record">"Iniciar rexistro"</string> string name="resume_record">"Rexistro resumido"</string> string name="start_play">"Iniciar reprodución"</string> string name="resume_play">"Resumir reprodución"
© de menos de 1 segundo, non gardado"</string> string name="file_path_button">Ruta do arquivo</string> string name="file_name">"Nome do arquivo:"</string> string name="file_path">"Ruta do arquivo:"</string> string name="rename">Renomear</string>
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(139)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-gl/strings.xml
© suficiente</string> string name="menu_recording_file_list">Rexistrando lista de arquivos</string> string name="recording_list_empty">Lista baleira</string> string name="menu_recording_list_select_all">Seleccionar todo</string> string name="menu_recording_list_delete">"Borrar"</string> string name="menu_recording_list_deselect_all">Deseleccionar</string> string name="recording_file_delete_success">"Gravación eliminada"</string> string name="recording_file_database_failed">Erro ó borrar o arquivo da base de datos</string> string name="recording_file_delete_failed">Erro ó borrar o arquivo o arquivo de rexistro</string> string name="confirm_del">¿Está seguro que desexa borrar os arquivos?</string> string name="button_delete">"Borrar"</string> string name="recording_file_delete_alert_title">"Borrar"</string> string name="recording_file_delete_alert_message">¿Seguro de borrar %1$s?</string> string name="recording_file_list">Rexistrando lista de arquivos</string> strin
© sobrescribible ou inválida, a súa ruta á ruta de almacenamento interno</string> string name="menu_set_save_path">Axustar ruta de memorización</string> string name="back">Volver...</string> string name="path_label">Seleccione a ruta para gardar</string> string name="pause">Pausa</string>
© insuficiente e non se poden cargar os datos de onda"</string> string name="file_does_not_exist">"O arquivo que se está a reproducir non existe"</string> resources>
© de menos de 1 segundo, non gardado"</string> string name="file_path_button">Ruta do arquivo</string> string name="file_name">"Nome do arquivo":</string> string name="file_path">"Ruta do arquivo:"</string> string name="rename">Renomear</string>
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(140)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-gu-rIN/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(141)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-gu/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(142)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ha/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(143)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-hi-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(144)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-hi-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(145)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-hi/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(146)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-hr-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(147)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-hr-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(148)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-hr/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(149)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-hu-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(150)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-hu-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(151)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-hu/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(152)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-hy-rAM/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(153)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-id-keysexposed/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(154)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-id-keyshidden/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(155)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-id/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(156)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-in-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(157)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-in-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(158)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-in/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(159)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-is-rIS/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(160)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-it-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(161)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-it-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(162)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-it/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(163)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-iw-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(164)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-iw-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(165)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-iw/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(166)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ja-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(167)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ja-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(168)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ja/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(169)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ka-rGE/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(170)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(171)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(172)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-kk-rKZ/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(173)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-km-keysexposed/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(174)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-km-keyshidden/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(175)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-km-rKH/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(176)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-km/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(177)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-kn-rIN/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(178)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-kn/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(179)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ko-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(180)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ko-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(181)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ko/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(182)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-kok-rIN/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(183)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ks-rIN/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(184)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ky-rKG/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(185)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-lo-rLA/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(186)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-lo/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(187)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-lt-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(188)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-lt-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(189)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-lt/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(190)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-lv-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(191)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-lv-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(192)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-lv/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(193)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-mai-rIN/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(194)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-mk-rMK/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(195)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ml-rIN/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(196)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ml/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(197)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-mn-rMN/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(198)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-mni-rIN/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(199)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-mr-rIN/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(200)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-mr/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(201)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ms-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(202)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ms-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(203)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ms-rMY/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(204)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ms/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(205)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-my-rZG/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(206)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-my/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(207)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-nb-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(208)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-nb-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(209)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-nb/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(210)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ne-rNP/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(211)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ne/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(212)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-night/colors.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(213)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-night/styles.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(214)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-nl-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(215)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-nl-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(216)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-nl/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(217)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-om-rET/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(218)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-or-rIN/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(219)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-or/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(220)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pa-rIN-keysexposed/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(221)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pa-rIN-keyshidden/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(222)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pa-rIN/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(223)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pa/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(224)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pl-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(225)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pl-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(226)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pl/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(227)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pt-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(228)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pt-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(229)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pt-rBR/strings.xml
© suficiente"</string> string name="menu_recording_file_list">"Lista de arquivo de gravação"</string> string name="recording_list_empty">"Lista vazia"</string> string name="menu_recording_list_select_all">"Selecionar tudo"</string> string name="menu_recording_list_delete">"Apagar"</string> string name="menu_recording_list_deselect_all">"Desmarcar"</string> string name="recording_file_delete_success">"Gravação excluída"</string> string name="recording_file_database_failed">"Falha ao excluir o arquivo de banco de dados"</string> string name="recording_file_delete_failed">"Falha ao excluir arquivo de gravação"</string> string name="confirm_del">"Você tem certeza de que deseja excluir os arquivos?"</string> string name="button_delete">"Apagar"</string> string name="recording_file_delete_alert_title">"Apagar"</string> string name="recording_file_delete_alert_message">"Você tem certeza de que deseja excluir %1$s?"</string> string name="recording_file_list">"Lista de a
© possível salvar o áudio gravado"</string> string name="save">"Salvar"</string> string name="select_file_type">"Selecionar o tipo de arquivo de registro"</string> string name="record_amr">"tipo AMR (Baixa-qualidade)"</string> string name="record_3gpp">"Tipo 3gpp (Alta-qualidade)"</string> string name="button_cancel">"Cancelar"</string> string name="path_save">"Caminho padrão salvo"</string> string name="path_nosave">"Caminho padrão não salvo"</string>
© possível ligar o gravador de som durante uma chamada."</string> string name="min_available">"Cerca de <xliff:g id="minutes">%d</xliff:g> minutos disponíveis"</string> string name="min_and_time_available">"Sobre <xliff:g id="minutes">%1$d</xliff:g> min e <xliff:g id="seconds">%2$d</xliff:g> segundo disponível"</string> string name="sec_available">"Cerca de <xliff:g id="minutes">%d</xliff:g>s disponíveis"</string> string name="accept">"Salvar"</string> string name="discard">"Descartar"</string> string name="button_ok">"OK"</string> string name="press_record">"Pressione gravar"</string> string name="audio_db_artist_name">"Suas gravações"</string> string name="audio_db_album_name">"Gravações de áudio"</string> string name="audio_db_playlist_name">"Minhas Gravações"</string> string name="error_app_internal">"Erro de aplicação interna"</string>
© permitido reproduzir música durante as chamadas (rádio/vídeo)."</string> string name="playback_failed">"O aparelho não suporta este tipo de arquivo de audio."</string> string name="tag">"Marcador"</string> string name="previous_tag">"Último marcador"</string> string name="next_tag">"Marcador seguinte"</string> string name="recording_play">"Reprodução de gravação"</string> string name="clip_duration_tip">"Editar comprimento"</string> string name="record_setting">"Definições de Gravação"</string> string name="file_type">"Tipo de Arquivo "</string> string name="type_amr">"tipo amr"</string> string name="type_3gpp">"tipo 3gpp"</string> string name="save_path">"Caminho armazenado"</string> string name="set_timer_recording">"Gravação programada"</string> string name="timer_recording_detail">"Começar a gravar às <xliff:g id="hours">%1$s</xliff:g>:<xliff:g id="minutes">%2$s</xliff:g> para <xliff:g id="COUNTS">%3$s</xliff:g>"</string> string name="view_file_de
© nem de armazenamento interno, nem de armazenamento externo. Caminho padrão para salvamento foi alterado para armazenamento interno."</string> string name="menu_set_save_path">"Definir caminho de salvamento"</string> string name="back">"Retroceder.."</string> string name="path_label">"Salvar para"</string> string name="pause">"Pausar"</string>
© insuficiente para salvar a forma de onda e etiqueta"</string>
© insuficiente e os dados de onda não podem ser carregados"</string> string name="file_does_not_exist">"O arquivo que está sendo reproduzido não existe"</string> resources>
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
© compatível com o modo de várias janelas, use o Gravador de som no modo de tela cheia."</string> string name="document_request_confirm">"Gravador de Som não tem acesso para usar o diretório que você escolheu; selecione o diretório &lt;Android&gt; e autorize o GravadorSom para operar."</string> string name="record_aac">"AAC (Alto Grau)"</string>
LicenseRef-Unisoc-General-1.0
(230)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pt-rPT-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(231)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pt-rPT-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(232)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pt-rPT/strings.xml
© usado por outro programa, favor feche este programa!"</string> string name="recording_time_short">Tempo de gravação menos de 1 segundo, não guardado</string> string name="file_path_button">"Via de arquivo"</string> string name="file_name">"Nome de arquivo:"</string> string name="file_path">"Via de arquivo:"</string> string name="rename">"Mudar o nome"</string> string name="rename_save">"Renomear com sucesso"</string> string name="rename_nosave">falha renomear </string>
© suficiente"</string> string name="menu_recording_file_list">"Lista de arquivo de gravação"</string> string name="recording_list_empty">"Lista vazia"</string> string name="menu_recording_list_select_all">Selecionar tudo</string> string name="menu_recording_list_delete">Deletar</string> string name="menu_recording_list_deselect_all">"Deleccionar"</string> string name="recording_file_delete_success">"Gravação Apagada"</string> string name="recording_file_database_failed">"Falha deletar arquivo de base de dados"</string> string name="recording_file_delete_failed">"Falha deletar arquivo de gravação"</string> string name="confirm_del">"Tem certeza deletar os arquivos?"</string> string name="button_delete">Deletar</string> string name="recording_file_delete_alert_title">Deletar</string> string name="recording_file_delete_alert_message">"Deseja deletar %1$s?"</string> string name="recording_file_list">"List
© suficiente, a gravação vai parar!"</string> string name="record_error">"Gravando um anormal de excepção!"</string> string name="play_error">"O aparelho de música já foi desactivado, não pode abrir!"</string> string name="dialog_title">"Seleccione"</string> string name="dialog_message">"Guardar a gravação?"</string>
© possível aceder ao cartão SD."</string> string name="error_app_internal" msgid="312884382388702022">"Erro interno da aplicação."</string> string name="error_mediadb_new_record" msgid="261714902333432462">"Não foi possível guardar o áudio gravado."</string> string name="recording_save">"Gravação guardada"</string> string name="recording_nosave">"Gravação não foi gravada"</string> string name="phone_message">"Não pode ligar Gravador de Som quando está chamando."</string> string name="min_and_time_available">"Sobre <xliff:g id="minutes">%1$d</xliff:g> min e <xliff:g id="seconds">%2$d</xliff:g> seg disponível"</string> string name="press_record">"Empurrar gravar"</string> string name="save">Guardar</string> string name="select_file_type">"Seleccionar o tipo de arquivo de gravação"</string> string name="record_amr">"AMR tipo(baixa-qualidade)"</string> string name="record_3gpp">"3gpp tipo(alta-qualidade)"</s
© mudada para a guarda internal."</string> string name="input_length_overstep">"Entrada alcançada o máx comprimento"</string> string name="filename_empty_error">"O nome de arquivo não pode ser vázio, guarde o nome original"</string> string name="please_choose_files">"Favor escolha os arquivos"</string> string name="selected_files_count">"foi escolhido<xliff:g id="COUNTS">%d</xliff:g>"</string> string name="use_default_path">"A via guardada não existe, use a default via"</string>
© montada"</string> string name="special_char_exist">"Carácteres especiais <xliff:g id="COUNTS">%s</xliff:g> não são suportados"</string> string name="menu_select_more">Selecionar mais</string> string name="menu_select_type">"Ajustar o tipo de arquivo"</string> string name="path_miss_nosave">"A via guardada não existe, a gravação não foi guardada!"</string> string name="dialog_dismiss">"Descartar"</string> string name="error_permissions">"A aplicação não tem autorizações críticas que são necessárias para ser executada. Verifique as definições das autorizações."</string> string name="internal_sdcard">"Sdcartão internal"</string> string name="external_sdcard">"Sdcartão external"</string> string name="internal_storage">Guarda internal</string> string name="external_storage">guarda external</string> string name="set_as_ring">"Definir como tom de toque"</string> string name="read_file_path">"Verific
© modificado"</string> string name="duplicate_name">"nome de arquivo duplicado"</string> string name="soundpicker_label">Favor seleccione o arquivo de gravação</string>
© insuficiente para salvar a forma de onda e a tag"</string>
© insuficiente e os dados de onda não podem ser carregados"</string> string name="file_does_not_exist">"O arquivo que está sendo reproduzido não existe"</string> resources>
© escrita nem inválida, é a via para a via de guarda internal"</string> string name="menu_set_save_path">"Definir percurso de guardar"</string> string name="back">"Voltar"</string> string name="path_label">"Favor seleccione percurso de guarda"</string> string name="pause">"Pausa"</string>
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(233)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-pt/strings.xml
© possível acessar o cartão SD."</string> string name="error_app_internal" msgid="312884382388702022">"Erro interno do app."</string> string name="error_mediadb_new_record" msgid="261714902333432462">"Não foi possível salvar o áudio gravado."</string> resources>
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(234)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-rm-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(235)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-rm-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(236)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-rm/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(237)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ro-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(238)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ro-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(239)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ro/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(240)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ru-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(241)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ru-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(242)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ru/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(243)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sa-rIN/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(244)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sat-rIN/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(245)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sd-rIN/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(246)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-si-rLK/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(247)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-si/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(248)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sk-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(249)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sk-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(250)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sk/strings.xml
© záznamy"</string> string name="audio_db_playlist_name" msgid="5592939050047058162">"Moje záznamy"</string> string name="error_sdcard_access" product="default" msgid="5750308258096153274">"Nedá sa získať prístup ku SD karte."</string> string name="error_app_internal" msgid="312884382388702022">"Vnútorná chyba aplikácie."</string> string name="error_mediadb_new_record" msgid="261714902333432462">"Zaznamenaný zvuk sa nepodarilo uložiť."</string> string name="recording_save">"Nahrávka uložená"</string> string name="recording_nosave">"Nahrávka nebola uložená"</string>
© uloženie cesty je zmenené na internú pamäť.</string> string name="input_length_overstep">"Zadaný text dosiahol maximálnu dĺžku"</string> string name="filename_empty_error">"Názov súboru nemôže byť prázdny, uložte pôvodný názov"</string> string name="please_choose_files">"prosím, zvoľte súbory"</string>
© prehrávať hudbu počas hovoru (rádio/video)."</string>
© povolenia potrebné na spustenie. Skontrolujte nastavenia povolení."</string>
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(251)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sl-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(252)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sl-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(253)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sl/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(254)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-so-rSO/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(255)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sq-rAL/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(256)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sq/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(257)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sr-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(258)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sr-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(259)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sr/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(260)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sv-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(261)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sv-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(262)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sv/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(263)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sw-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(264)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sw-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(265)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-sw/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(266)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ta-rIN/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(267)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ta/strings.xml
© SANDISK SD கார்டைத் தேர்ந்தெடுத்து, செயல்பட சவுண்ட் ரெக்கார்டருக்கு அங்கீகாரம் அளிக்கவும்."</string>
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(268)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-te-rIN/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(269)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-te/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(270)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-th-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(271)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-th-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(272)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-th/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(273)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ti-rET/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(274)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-tl-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(275)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-tl-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(276)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-tl/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(277)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-tr-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(278)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-tr-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(279)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-tr/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(280)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ug/strings.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(281)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-uk-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(282)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-uk-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(283)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-uk/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(284)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ur-rPK/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(285)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-ur/strings.xml
© SD کارڈ داخل کریں</string>
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(286)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-uz-rUZ/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(287)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-vi-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(288)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-vi-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(289)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-vi/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(290)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-zh-rCN-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(291)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-zh-rCN-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(292)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-zh-rCN/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(293)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-zh-rHK/strings.xml
Copyright (C) 2007 The Android Open Source Project Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
Apache-2.0
(294)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-zh-rTW-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(295)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-zh-rTW-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(296)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-zh-rTW/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(297)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-zu-keysexposed/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(298)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-zu-keyshidden/strings.xml
Copyright 2007, The Android Open Source Project
Apache-2.0
(299)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values-zu/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(300)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values/attrs.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(301)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values/colors.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(302)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values/dimens.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(303)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values/ic_launcher_background.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(304)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values/strings.xml
Copyright (C) 2007 The Android Open Source Project
Apache-2.0
(305)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/values/styles.xml
Copyright (C) 2011 The Android Open Source Project
Apache-2.0
(306)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/res/xml/setting_preference.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(307)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/settings.gradle
NOASSERTION
NOASSERTION
(308)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/EmojiUtil.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(309)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/MultiChooseActivity.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(310)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/RecordFilePlayActivity.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(311)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/RecordListActivity.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(312)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/RecordPreviewPlayer.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(313)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/RecorderActivity.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(314)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/RecorderApplication.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(315)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/RecorderReceiver.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(316)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/RecorderTimerReceiver.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(317)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/SdcardPermission.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(318)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/SettingActivity.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(319)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/SlidingTabLayout.java
Copyright 2014 Google Inc. All rights reserved.
Apache-2.0
(320)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/SlidingTabStrip.java
Copyright 2014 Google Inc. All rights reserved.
Apache-2.0
(321)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/SoundPicker.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(322)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/StorageInfos.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(323)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/Utils.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(324)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/data/DataOpration.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(325)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/data/RecordListAdapter.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(326)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/data/RecorderItem.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(327)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/data/ViewHolder.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(328)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/data/WaveDataManager.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(329)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/frameworks/EnvironmentEx.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(330)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/frameworks/SprdFramewoks.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(331)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/frameworks/StandardFrameworks.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(332)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/service/RecordingService.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(333)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/service/SprdRecorder.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(334)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/ui/MarkSeekBar.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(335)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/ui/RecordItemFragment.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(336)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/ui/RecordWaveView.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(337)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/ui/SmartSwitchPreference.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(338)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/ui/TimeAndDurationPickerDialog.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(339)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/src/com/sprd/soundrecorder/ui/TimePickerFragment.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(340)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/androidTest/Android.mk
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0 LOCAL_PATH:= $(call my-dir) src_dirs := java include $(CLEAR_VARS) We only want this apk build for tests. LOCAL_MODULE_TAGS := tests LOCAL_CERTIFICATE := platform LOCAL_STATIC_JAVA_LIBRARIES := \ androidx.test.rules \ androidx.test.runner \ androidx.test.uiautomator_uiautomator \ androidx.test.core LOCAL_SDK_VERSION := current LOCAL_PACKAGE_NAME := APP.DreamSoundRecorder_IT_Tests LOCAL_INSTRUMENTATION_FOR := DreamSoundRecorder LOCAL_SRC_FILES := $(call all-java-files-under, $(src_dirs)) LOCAL_RESOURCE_DIR := $(addprefix $(LOCAL_PATH)/, $(res_dirs)) LOCAL_AAPT_FLAGS := --auto-add-overlay LOCAL_COMPATIBILITY_SUITE := units include $(BUILD_PACKAGE) Build all sub-directories include $(call all-makefiles-under,$(LOCAL_PATH))
LicenseRef-Unisoc-General-1.0
(341)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/androidTest/AndroidManifest.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(342)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/androidTest/AndroidTest.xml
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(343)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/androidTest/java/com/android/soundrecorder/MultiChoosActivityTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(344)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/androidTest/java/com/android/soundrecorder/RecorderActivityTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(345)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/androidTest/java/com/android/soundrecorder/RecorderListActivityTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(346)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/androidTest/java/com/android/soundrecorder/RecorderPlayActivityTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(347)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/androidTest/java/com/android/soundrecorder/SettingActivityTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(348)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/androidTest/java/com/android/soundrecorder/SoundPickerTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(349)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/androidTest/java/com/android/soundrecorder/VerifyToastUtil.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(350)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/MultiChooseActivityTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(351)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/RecordFilePlayActivityTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(352)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/RecordListActivityTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(353)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/RecorderActivityTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(354)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/SettingActivityTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(355)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/ShadowAudioManager.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(356)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/ShadowDataOpration.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(357)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/ShadowSprdFrameworks.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(358)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/ShadowSprdRecorder.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(359)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/ShadowStandardFrameworks.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(360)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/ShadowUtils.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(361)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/SoundPickerTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(362)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/java/com/sprd/soundrecorder/service/RecordingServiceTest.java
CopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LicenseRef-Unisoc-General-1.0
(363)/vendor/sprd/platform/packages/apps/DreamSoundRecorder/tests/test/resources/robolectric.properties
NOASSERTION
NOASSERTION
