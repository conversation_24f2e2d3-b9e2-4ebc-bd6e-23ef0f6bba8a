apply plugin: 'jacoco'

tasks.withType(Test) {
    jacoco.includeNoLocationClasses = true
    jacoco.excludes = ['jdk.internal.*']
}

jacoco {
    toolVersion = "0.8.2"
}

/**
 coverageSourceDirs：源代码路径
 */
def coverageSourceDirs = [
        "${projectDir}/src/main/java"
]

task jacocoTestReport(type: JacocoReport, dependsOn: "testDebugUnitTest") {
    group = "Reporting"
    description = "Generate Jacoco coverage reports"
    reports {
        xml.enabled = false
        html.enabled = true
    }

    additionalSourceDirs.setFrom(files(coverageSourceDirs))
    sourceDirectories.setFrom(files(coverageSourceDirs))
    classDirectories.setFrom(fileTree(
            dir: "${buildDir}/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/sprd/soundrecorder",
    ))
    executionData.setFrom(files("${buildDir}/jacoco/testDebugUnitTest.exec"))
}
