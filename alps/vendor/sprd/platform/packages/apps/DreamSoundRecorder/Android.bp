//
// SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
// SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
android_app {
    name: "DreamSoundRecorder",

    system_ext_specific: true,
    overrides: ["SoundRecorder"],
    certificate: "platform",
    resource_dirs: ["res"],
    aaptflags: ["--auto-add-overlay"],
    manifest: "app/AndroidManifest.xml",

    //LOCAL_JAVA_LIBRARIES += sprd-framework
    libs: ["unisoc-framework"],

    srcs: [
       "src/**/*.java",
       "app/libs/aspectjrt-1.8.2.jar",
       "app/libs/isoparser-1.1.7.jar",
    ],

    platform_apis: true,

    static_libs: [
        "androidx.legacy_legacy-support-v13",
        "androidx.legacy_legacy-support-v4",
        "androidx.appcompat_appcompat",
        "com.google.android.material_material",
        // "isoparser",
        // "aspectjrt",
    ],

    optimize: {
        obfuscate: true,
        optimize: true,
        enabled: true,
    },
}

package {
    default_applicable_licenses: ["DreamSoundRecorder_license"],
}

license {
    name: "DreamSoundRecorder_license",
    license_text: [
        "Notice.DreamSoundRecorder",
        "License.DreamSoundRecorder",
    ],
}