Component: DreamSoundRecorder

This component contains a series of licenses, all the agreement content of each are as follows.

You may also visit the official links provided by each of them (if any) for more information.

Apache-2.0
                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

LicenseRef-Unisoc-General-1.0
Unisoc General Software License
version 1.0, June 2022
https://www.unisoc.com/en_us/license/UNISOC_GENERAL_LICENSE_V1.0-EN_US

The Unisoc General Software License v1 (hereinafter referred to as this License) is formulated by Unisoc (Shanghai) Technologies Co., Ltd. (hereinafter referred to as UNISOC), and UNISOC reserves the intellectual property rights of this License and the right of final interpretation of all its terms.
You may apply this License to your Works without any modification, provided that you must obtain the prior written consent of UNISOC.
Any Use or Distribution of the Works to which this License has been applied to shall mean that you have expressly assented to this License, that assent indicates your clear and irrevocable acceptance of this License and all of its terms and conditions.
Works, Use, Distribution and other related terms are defined below.

0. Definitions
Licensor shall mean any individual or Legal Entity who license the Works to you under this License. 
You (or Your), whether in upper or lower case, shall mean any individual or Legal Entity exercising permissions granted by this License.
Legal Entity shall mean economic entity with legal person status and its Affiliates.
Affiliate shall mean any other individual or Legal Entity that directly or indirectly controls, is controlled by, or is under common control with a Legal Entity.
Works shall mean the works of authorship and its copies, whatever the form, made available under this License.
Derivative Works shall mean any new works and its copies derived from the whole or part of the Works, whatever the form, including but not limited to editorial revisions, annotations, elaborations, or other modifications of the whole or part of the Works. For the purposes of this License, Derivative Works shall not include works remaining separable from, or merely link to the interfaces of, the Works and Derivative Works thereof.
Source Form shall mean the preferred form for making modifications, including but not limited to software source code, documentation source, and configuration files.
Object Form shall mean any form resulting from mechanical transformation or translation of a Source Form, including but not limited to compiled object code, generated documentation, and conversions to other media types.
Compile shall mean the process of translating programs in Source Form into programs in Object Form.
Use shall only mean Duplicating, Modifying, or Compiling the Source Form of the Works, and Running the Object Form of the Works.
Distribute/Distribution shall only mean the act of making the Works and its Derivative Works available to others in the following manners: (1) Distribute the Work in a way that is embedded in your product which uses Unisoc Chip. (2) Distribute the Work in a way that is installable for the end users of your product which uses Unisoc Chip. (3) Distribute the Work by SaaS.
SaaS shall mean Software as a Service which allows users to connect to and use cloud-based apps of the Works over the Internet.
Sublicense shall mean the act that any individual or Legal Entity authorized by this License will license the rights permitted by this License to any other individual or Legal Entity.

1. Conditional Copyright License
The Licensor grants you a license to Use the Works without the right to Sublicense such right, provided that the following conditions are met: 
(1) You should Use the Works without any breach of the written consent (such as a commercial contract) that you must obtain from the Licensor first.
(2) For Legal Entity, you should ensure those who access to the Works because of their relationship with you will Use the Works exclusively on your behalf, as well as under your direction and control, and on terms that prohibit any unauthorized conduct by them.
The Licensor grants you a license to Distribute the Works and its Derivative Works with the right to Sublicense such right, provided that the following conditions are met:
(1) You should Distribute the Works and its Derivative Works without any breach of the written consent (such as a commercial contract) that you must obtain from the Licensor first.
(2) You should Distribute the Works and its Derivative Works only in Object Form.
(3) You should ensure that this License applies to the Works and that any act by you relating to the Works will not affect the effectiveness of this License.
(4) You should ensure that this License applies to the Derivative Works you create, recipients are conspicuously notified that such Derivative Works are derived from the Works, and retain, in the Derivative Works that you create, all copyright, patent, or trademark notices from the Works, as well as any other descriptive text regarding attribution of rights.
You may have additional license terms from the Licensor other than this License.

2. No Patent License
For the purposes of this License alone, the Licensor does not grant any patent license relates to the Works.

3. No Trademark License
For the purposes of this License alone, the Licensor does not grant any trademark license for its trade names, trademarks, service identifier or product names, except as necessary to satisfy your declarative obligations under this License.

4. Breach and Termination
Any breach of any term of this License will immediately terminate your rights under this License.

5. Disclaimer
The Licensor has not made any express or implied covenants or warranties for the Works or Derivative Works, including, but not limited to, warranties for merchantability or fit for a particular purpose. In no event shall the Licensor have any liability for any direct or indirect losses caused to anyone using the Works or Derivative Works, regardless of the reason for the loss or be it based on any legal theory, even if it has been informed of the possibility of such losses.

6. Rights Reserved
The Licensor reserves any right not expressly granted in this License text.

END OF TERMS


APPENDIX: How to apply Unisoc General Software License v1 to your software
If you desire to apply Unisoc General Software License v1 to your software, in order to facilitate the recipient’s reading, it is recommended that you should complete the following three steps:
1. Please attach the following boilerplate declaration, replacing the fields enclosed by brackets "[]" & "<>" with your own identifying information, where "[]" means optional content, and "<>"means mandatory content.
2. Please create a folder named LICENSE in the primary directory of the software package, and put the entire license text into the folder.
3. Please put the following statement text in the header comment of each source file.

Copyright <First_Publication_In_Year>[-<Last_Update_In_Year>] <Name of Copyright Owner> 
Licensed under the Unisoc General Software License,
version 1.0 (the License); 
you may not use this file except in compliance with the License.
You may obtain a copy of the License at
https://www.unisoc.com/en_us/license/UNISOC_GENERAL_LICENSE_V1.0-EN_US
Software distributed under the License is distributed on an "AS IS" BASIS, 
WITHOUT WARRANTIES OF ANY KIND, either express or implied.
See the Unisoc General Software License, version 1.0 for more details.

