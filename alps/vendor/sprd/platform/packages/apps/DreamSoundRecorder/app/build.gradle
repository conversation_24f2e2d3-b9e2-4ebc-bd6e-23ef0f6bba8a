apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply from: '../jacoco-report.gradle'

android {
    compileSdkVersion 30
    defaultConfig {
        applicationId "com.android.soundrecorder"
        minSdkVersion 30
        targetSdkVersion 30
        //Gradle编译时，versionCode需要大于或等于mk编译的code,否则 adb install 会报version错误
        versionCode 30
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    testOptions {
        unitTests {
            includeAndroidResources = true
            //让Gradle系统为该方法返回默认值
            returnDefaultValues = true
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            testCoverageEnabled true
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        coverageDebug {
            minifyEnabled false
            testCoverageEnabled true
        }
    }

    signingConfigs {
        debug {
            storeFile file('../platform.keystore')
            storePassword 'android'
            keyAlias = 'platform'
            keyPassword 'android'
        }
    }

    sourceSets {
        main {
            java.srcDirs = ['../src']
            resources.srcDirs = ['../src']
            aidl.srcDirs = ['../src']
            renderscript.srcDirs = ['../src']
            res.srcDirs = ['../res']
            assets.srcDirs = ['../assets']
        }
        test.setRoot('../tests/test')
        androidTest.setRoot('../tests/androidTest')
    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            outputFileName = "DreamSoundRecorder.apk"
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation files('libs/aspectjrt-1.8.2.jar')
    implementation files('libs/isoparser-1.1.7.jar')

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.google.android.material:material:1.2.0-alpha02'
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.core:core-ktx:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'androidx.documentfile:documentfile:1.0.0'
    implementation 'androidx.test:core:1.2.0'

    testImplementation 'junit:junit:4.12'
    testImplementation 'org.robolectric:robolectric:4.5'
    testImplementation 'org.mockito:mockito-core:1.9.5'
    testImplementation 'org.powermock:powermock-api-mockito:1.5.6'

    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation 'androidx.test.uiautomator:uiautomator:2.2.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'

}