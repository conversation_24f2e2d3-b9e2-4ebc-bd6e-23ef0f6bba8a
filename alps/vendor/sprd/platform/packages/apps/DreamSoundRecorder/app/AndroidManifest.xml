<?xml version="1.0" encoding="utf-8"?>

<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.android.soundrecorder"
    android:versionName="13">

    <!-- Bug 1935203 Increase permissions, prohibit third-party apk access -->
    <permission android:name="com.android.soundrecorder.receiver.service.permission.ACCESS"
        android:protectionLevel="signatureOrSystem"></permission>
    <!-- 2267854 start {@ -->
    <permission android:name="com.android.permission.RECORD_LIST_PERMISSION"
        android:protectionLevel="signature|privileged" />
    <!-- @}-->
    <original-package android:name="com.android.soundrecorder" />
    <!-- Bug 1935203 Increase permissions, prohibit third-party apk access -->
    <uses-permission android:name="com.android.soundrecorder.receiver.service.permission.ACCESS"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.UNLIMITED_TOASTS" />

    <!-- Redmine 300554 fix sound recorder rename failed by zhangzhiyong 2024/11/21 begin -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <!-- Redmine 300554 fix sound recorder rename failed by zhangzhiyong 2024/11/21 end -->

    <!-- SPRD: add -->
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SET_MEDIA_KEY_LISTENER" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE"/>
    <!-- 2267854 start {@ -->
    <uses-permission android:name="com.android.permission.RECORD_LIST_PERMISSION" />
    <!-- @}-->

    <!-- Bug 2274941 Used to trigger the trace start/stop service. -->
    <uses-permission android:name="android.permission.START_FOREGROUND_SERVICES_FROM_BACKGROUND"/>

    <application
        android:name="com.sprd.soundrecorder.RecorderApplication"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:resizeableActivity="false"
        android:supportsRtl="true"
        android:hardwareAccelerated="false"
        android:usesCleartextTraffic="false">
        <activity
            android:name="com.sprd.soundrecorder.RecorderActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|screenLayout"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Base">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.provider.MediaStore.RECORD_SOUND" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <!-- Bug 1935203 chanage false -->
        <service
            android:name="com.sprd.soundrecorder.service.RecordingService"
            android:exported="false"
            android:foregroundServiceType="microphone">
            <intent-filter>
                <action android:name="com.android.soundrecorder.timerrecord.START" />
            </intent-filter>
        </service>
        <!-- SPRD: add @{ -->

        <activity
            android:name="com.sprd.soundrecorder.RecordListActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:permission="com.android.permission.RECORD_LIST_PERMISSION"
            android:label="@string/recording_file_list"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="com.android.soundrecorder.EXTRA_FILE_LIST" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.sprd.soundrecorder.MultiChooseActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|locale|layoutDirection"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"></activity>

        <activity
            android:name="com.sprd.soundrecorder.SoundPicker"
            android:configChanges="orientation|screenSize|keyboardHidden|mcc|mnc"
            android:label="@string/soundpicker_label"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.SoundPicker"></activity>

        <activity
            android:name="com.sprd.soundrecorder.RecordFilePlayActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|uiMode"
            android:label="@string/recording_play"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.SoundRecorder"></activity>

        <activity
            android:name="com.sprd.soundrecorder.SettingActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:label="@string/record_setting"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.RecordingFileList"></activity>

        <receiver
            android:name="com.sprd.soundrecorder.RecorderReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.LOCALE_CHANGED" />
            </intent-filter>
        </receiver>
        <!-- Bug 1935203 Increase permissions, prohibit third-party apk access -->
        <receiver android:name="com.sprd.soundrecorder.RecorderTimerReceiver"
            android:exported="true"
            android:permission="com.android.soundrecorder.receiver.service.permission.ACCESS">
            <intent-filter>
                <action android:name="com.android.soundrecorder.timerrecord.START" />
            </intent-filter>
        </receiver>

        <!-- A01-1595 modify Share audio file by hanmateng 20250728 begin -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.android.soundrecorder.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>
        <!-- A01-1595 modify Share audio file by hanmateng 20250728 end -->
    </application>

</manifest>
