/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.android.soundrecorder;

import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.runner.AndroidJUnit4;
import androidx.test.uiautomator.*;
import org.junit.Assert;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * perform condition:
 * 1.Home activity needs: settings, email, soundrecorder,phonecall
 * 2.The status bar needs: music player、screenrecorder
 * 3.Cmcc Sim card required
 */
@RunWith(AndroidJUnit4.class)
public class SoundPickerTest {

    private static final int LAUNCH_TIMEOUT = 500;
    private static final String mPackageName = "com.android.soundrecorder";
    private static final String mEmailPackage = "com.android.email";
    private UiDevice mDevice;
    private Context context;

    @Before
    public void SetUp() throws Exception {
        mDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());
        try {
            if (!mDevice.isScreenOn()) {
                mDevice.wakeUp();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        //Start from home
        mDevice.pressHome();
        // Wait for launcher
        final String launcherPackage = mDevice.getLauncherPackageName();
        Assert.assertNotNull(launcherPackage);
        mDevice.wait(Until.hasObject(By.pkg(launcherPackage).depth(0)), LAUNCH_TIMEOUT);
        // launch the email app
        context = ApplicationProvider.getApplicationContext();
        try {
            //if there is no email skip all test
            final Intent intent = context.getPackageManager()
                    .getLaunchIntentForPackage(mEmailPackage);
            context.startActivity(intent);
        } catch (NullPointerException e) {
            e.printStackTrace();
            return;
        }
        try {
            UiObject editButton = mDevice.findObject(new UiSelector().resourceId("com.android.email:id/compose_button"));
            editButton.click();
			UiObject addAttachment = mDevice.findObject(new UiSelector().resourceId("com.android.email:id/add_attachment"));
			addAttachment.click();
			UiObject soundrecorder = mDevice.findObject(new UiSelector().text("Sound Recorder"));
			soundrecorder.click();
		} catch (UiObjectNotFoundException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testRecordingFromEmail() throws Exception {
        //start recording
        //if email fail to launch skip test
        try {
            UiObject startAndPauseButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recordButton"));
            startAndPauseButton.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        UiObject recordName1 = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/filename"));
        String recordName = recordName1.getText();
        System.out.println("recordName:" + recordName);
        //stop recording
        UiObject stopButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stopButton"));
        stopButton.click();
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        UiObject attachmentName = mDevice.findObject(new UiSelector().resourceId("com.android.email:id/attachment_name"));
        try {
            System.out.println("attachmentName:" + attachmentName.getText());
            Assert.assertTrue(attachmentName.getText().contains(recordName));
            //remove recording
            UiObject remove = mDevice.findObject(new UiSelector().resourceId("com.android.email:id/remove_attachment"));
            remove.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSelectRecord() throws Exception {
        //start recordListActivity
        //if email fail to launch skip test
        try {
            UiObject stopButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stopButton"));
            stopButton.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        //select the first record
        UiObject recordItem = mDevice.findObject(new UiSelector().resourceId("android:id/list")).getChild(new UiSelector().className("android.widget.LinearLayout").index(0));
        recordItem.click();
        String recordName = recordItem.getChild(new UiSelector().resourceId("com.android.soundrecorder:id/picker_displayname")).getText();
        recordItem.click();
        UiObject okButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/btnEnsure"));
        okButton.click();

        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        UiObject attachmentName = mDevice.findObject(new UiSelector().resourceId("com.android.email:id/attachment_name"));
        Assert.assertTrue(attachmentName.getText().contains(recordName));
        //remove recording
        UiObject remove = mDevice.findObject(new UiSelector().resourceId("com.android.email:id/remove_attachment"));
        remove.click();
    }

    @Test
    public void testSettingFromEmail() throws Exception {
        //start settingActivity
        //if email fail to launch skip test
        try {
            UiObject tagButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/tagButton"));
            tagButton.clickAndWaitForNewWindow();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        UiSelector listView = new UiSelector().className("android.widget.FrameLayout").instance(1)
                .childSelector(new UiSelector().className("android.widget.ListView"));
        UiObject typePreference = mDevice.findObject(listView.childSelector(new UiSelector().className("android.widget.LinearLayout")
                .instance(0)));
        Assert.assertTrue(typePreference.isEnabled());
    }

    @After
    public void tearDown() throws Exception {
        mDevice.pressBack();
    }
}
