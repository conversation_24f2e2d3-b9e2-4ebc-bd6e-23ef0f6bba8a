<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<!-- package name must be unique so suffix with "tests" so package loader doesn't ignore us -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.android.soundrecorder.tests">
    <!-- We add an application tag here just so that we can indicate that
    this package needs to link against the android.test library,
    which is needed when building test cases. -->
    <application>
        <uses-library android:name="android.test.runner" />
    </application>
    <!--
    This declares that this app uses the instrumentation test runner targeting
    the package of com.android.soundrecorder. To run the tests use the command:
    "adb shell am instrument -w
     com.android.soundrecorder.tests/androidx.test.runner.AndroidJUnitRunner"
    -->
    <instrumentation
        android:name="androidx.test.runner.AndroidJUnitRunner"
        android:label="Tests for SoundRecorder."
        android:targetPackage="com.android.soundrecorder" />
</manifest>