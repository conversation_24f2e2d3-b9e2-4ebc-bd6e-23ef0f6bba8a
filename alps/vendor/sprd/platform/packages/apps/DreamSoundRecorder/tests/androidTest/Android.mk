# SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
# SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
LOCAL_PATH:= $(call my-dir)
src_dirs := java
include $(CLEAR_VARS)
# We only want this apk build for tests.
LOCAL_MODULE_TAGS := tests
LOCAL_CERTIFICATE := platform
LOCAL_STATIC_JAVA_LIBRARIES := \
      androidx.test.rules \
      androidx.test.runner \
      androidx.test.uiautomator_uiautomator \
      androidx.test.core
LOCAL_SDK_VERSION := current
LOCAL_PACKAGE_NAME := APP.DreamSoundRecorder_IT_Tests
LOCAL_INSTRUMENTATION_FOR := DreamSoundRecorder
LOCAL_SRC_FILES := $(call all-java-files-under, $(src_dirs))
#LOCAL_RESOURCE_DIR := $(addprefix $(LOCAL_PATH)/, $(res_dirs))
LOCAL_AAPT_FLAGS := --auto-add-overlay
LOCAL_COMPATIBILITY_SUITE := units
include $(BUILD_PACKAGE)
# Build all sub-directories
include $(call all-makefiles-under,$(LOCAL_PATH))