/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.app.AlertDialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.preference.ListPreference;
import android.preference.SwitchPreference;
import android.widget.Button;
import android.widget.NumberPicker;
import com.android.soundrecorder.R;
import com.sprd.soundrecorder.service.RecordingService;
import com.sprd.soundrecorder.ui.SmartSwitchPreference;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.support.membermodification.MemberModifier;
import org.robolectric.Robolectric;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.Shadows;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.*;

import java.util.Calendar;

import static org.junit.Assert.*;

@RunWith(RobolectricTestRunner.class)
@Config(shadows = {ShadowSprdFrameworks.class})
public class SettingActivityTest {
    private SettingActivity settingActivity;
    private ActivityController<SettingActivity> mController;
    private SettingActivity.RecordSettingPreference settingPreferenceFragment;

    private final static String SOUNDREOCRD_TYPE_AND_DTA = "soundrecord.type.and.data";
    private final static String SAVE_RECORD_TYPE_INDEX = "recordType_index";
    private final static String SAVE_STORAGE_PATH = "storagePath";
    private final static String AUTO_SAVE_FILE_TYPE = "save_file";
    private static final String AUDIO_AAC_ADTS = "audio/aac-adts";
    private static final String INTERNAL_PATH = "\\storage\\emulated\\0\\Android\\media\\com.android.soundrecorder\\recordings";
    private static final int TIMER_HOUR = 13;
    private static final int TIMER_MINUTE = 14;

    @Mock
    private RecordingService mRecordingService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        mController = Robolectric.buildActivity(SettingActivity.class);
        settingActivity = mController.create().start().visible().resume().get();
        MemberModifier.field(SettingActivity.class, "mService")
                .set(settingActivity, mRecordingService);
        settingPreferenceFragment = (SettingActivity.RecordSettingPreference) settingActivity
                .getFragmentManager().findFragmentById(R.id.setting_content);

    }

    @Test
    public void testActivityLifecycle() {
        mController.get().setTheme(R.style.Theme_RecordingFileList);
        mController.create();
        mController.start();
        mController.resume();
        mController.pause();
        mController.stop();
        mController.destroy();
    }

    @Test
    public void testRecordType() {
        SharedPreferences sharedPreferences = RuntimeEnvironment.application
                .getSharedPreferences(SOUNDREOCRD_TYPE_AND_DTA, Context.MODE_PRIVATE);
        int defaultType = sharedPreferences.getInt(SAVE_RECORD_TYPE_INDEX, 0);
        assertEquals(0, defaultType);

        ListPreference typePreference = (ListPreference) settingPreferenceFragment.findPreference("pref_file_type_key");
        assertNotNull(typePreference);
        assertEquals("Format", typePreference.getTitle().toString());
        settingPreferenceFragment.onPreferenceChange(typePreference, AUDIO_AAC_ADTS);

        int afterType = sharedPreferences.getInt(SAVE_RECORD_TYPE_INDEX, 0);
        assertEquals(1, afterType);
    }

    @Test
    public void testRecordPath() throws Exception {
        SharedPreferences sharedPreferences = RuntimeEnvironment.application
                .getSharedPreferences(SOUNDREOCRD_TYPE_AND_DTA, Context.MODE_PRIVATE);
        String defaultPath = sharedPreferences.getString(SAVE_STORAGE_PATH, "");
        assertEquals(INTERNAL_PATH, defaultPath);

        ListPreference pathPreference = (ListPreference) settingPreferenceFragment.findPreference("pref_store_path_key");
        assertNotNull(pathPreference);
        assertEquals("Save location", pathPreference.getTitle().toString());
        settingPreferenceFragment.onPreferenceChange(pathPreference, INTERNAL_PATH);

        String afterPath = sharedPreferences.getString(SAVE_STORAGE_PATH, "");
        assertEquals(INTERNAL_PATH, afterPath);
    }

    @Test
    public void testAutoSave() throws Exception {
        SharedPreferences sharedPreferences = RuntimeEnvironment.application
                .getSharedPreferences(SOUNDREOCRD_TYPE_AND_DTA, Context.MODE_PRIVATE);
        boolean defaultSaveType = sharedPreferences.getBoolean(AUTO_SAVE_FILE_TYPE, false);
        assertFalse(defaultSaveType);

        SwitchPreference autoSavePreference = (SwitchPreference) settingPreferenceFragment.findPreference("pref_auto_save_key");
        assertNotNull(autoSavePreference);
        assertEquals("Auto save", autoSavePreference.getTitle().toString());

        //method one:preference click listener
        settingPreferenceFragment.onPreferenceClick(autoSavePreference);
        boolean autoSaveType1 = sharedPreferences.getBoolean(AUTO_SAVE_FILE_TYPE, false);
        assertTrue(autoSaveType1);

        autoSavePreference.setChecked(false);
        settingPreferenceFragment.onPreferenceClick(autoSavePreference);
        boolean noAutoSaveType1 = sharedPreferences.getBoolean(AUTO_SAVE_FILE_TYPE, false);
        assertFalse(noAutoSaveType1);

        //moethod two:preference change listener
        settingPreferenceFragment.onPreferenceChange(autoSavePreference, true);
        boolean autoSaveType2 = sharedPreferences.getBoolean(AUTO_SAVE_FILE_TYPE, false);
        assertTrue(autoSaveType2);

        settingPreferenceFragment.onPreferenceChange(autoSavePreference, false);
        boolean noAutoSaveType2 = sharedPreferences.getBoolean(AUTO_SAVE_FILE_TYPE, false);
        assertFalse(noAutoSaveType2);
    }

    @Test
    public void testTimingRecord() throws Exception {
        SmartSwitchPreference timerPreference = (SmartSwitchPreference) settingPreferenceFragment.findPreference("pref_set_timer_key");
        assertNotNull(timerPreference);
        assertEquals("Scheduled recording", timerPreference.getTitle().toString());
        settingPreferenceFragment.onPreferenceClick(timerPreference);

        AlertDialog alertDialog = ShadowAlertDialog.getLatestAlertDialog();
        assertNotNull(alertDialog);
        ShadowAlertDialog shadowAlertDialog = Shadows.shadowOf(alertDialog);
        assertEquals("Start time & duration", shadowAlertDialog.getTitle().toString());

        NumberPicker np1 = alertDialog.findViewById(R.id.np1);
        assertNotNull(np1);
        ShadowNumberPicker snp1 = Shadows.shadowOf(np1);
        NumberPicker.OnValueChangeListener listener = snp1.getOnValueChangeListener();
        listener.onValueChange(np1, 0, TIMER_HOUR);

        NumberPicker np2 = alertDialog.findViewById(R.id.np2);
        assertNotNull(np2);
        ShadowNumberPicker snp2 = Shadows.shadowOf(np2);
        NumberPicker.OnValueChangeListener listener2 = snp2.getOnValueChangeListener();
        listener2.onValueChange(np2, 0, TIMER_MINUTE);

        NumberPicker np3 = alertDialog.findViewById(R.id.np3);
        assertNotNull(np3);
        ShadowNumberPicker snp3 = Shadows.shadowOf(np3);
        NumberPicker.OnValueChangeListener listener3 = snp3.getOnValueChangeListener();
        listener3.onValueChange(np3, 0, 1);

        Button okButton = alertDialog.getButton(AlertDialog.BUTTON_POSITIVE);
        assertNotNull(okButton);
        okButton.performClick();

        assertNotNull(ShadowToast.getLatestToast());
        assertEquals(caculateTimer(), ShadowToast.getTextOfLatestToast());
    }

    private String caculateTimer() {
        Calendar c = Calendar.getInstance();
        int nowHour = c.get(Calendar.HOUR_OF_DAY);
        int nowMinute = c.get(Calendar.MINUTE);
        if (TIMER_HOUR < nowHour
                || (TIMER_HOUR == nowHour && TIMER_MINUTE <= nowMinute)) {
            c.add(Calendar.DAY_OF_YEAR, 1);
        }
        c.set(Calendar.HOUR_OF_DAY, TIMER_HOUR);
        c.set(Calendar.MINUTE, TIMER_MINUTE);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        long timerDelta = c.getTimeInMillis() - System.currentTimeMillis();
        return Utils.formatElapsedTimeUntilAlarm(RuntimeEnvironment.application, timerDelta);
    }

}