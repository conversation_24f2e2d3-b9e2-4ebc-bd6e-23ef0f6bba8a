/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.Manifest;
import android.app.AlertDialog;
import android.content.*;
import android.os.Handler;
import android.view.KeyEvent;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;
import com.android.soundrecorder.R;
import com.sprd.soundrecorder.service.RecordingService;
import com.sprd.soundrecorder.service.SprdRecorder;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.robolectric.Robolectric;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.Shadows;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.android.controller.ServiceController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.*;

import javax.annotation.meta.When;
import java.lang.reflect.Field;
import java.time.Duration;

import static org.junit.Assert.*;


@RunWith(RobolectricTestRunner.class)
@Config(shadows = {ShadowSprdFrameworks.class})
public class RecorderActivityTest {
    private RecorderActivity recorderActivity;
    private ActivityController<RecorderActivity> mController;
    private RecordingService.RecordState recordState;
    private ServiceController<RecordingService> mServiceController;
    private RecordingService mService;
    private ShadowLooper mShadowLooper;
    private ImageButton mStartOrPauseRecordBtn;
    private Button mTagAndSettingBtn;
    private Button mStopAndListBtn;
    private String[] permissions = {Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_PHONE_STATE};
    @Mock
    private ServiceConnection serviceConnection;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        mController = Robolectric.buildActivity(RecorderActivity.class);
        mController.create().visible();
        recorderActivity = mController.get();
        mStartOrPauseRecordBtn = recorderActivity.findViewById(R.id.recordButton);
        mTagAndSettingBtn = recorderActivity.findViewById(R.id.tagButton);
        mStopAndListBtn = recorderActivity.findViewById(R.id.stopButton);
        recordState = new RecordingService.RecordState(RecordingService.State.IDLE_STATE, RecordingService.State.IDLE_STATE);

        mServiceController = Robolectric.buildService(RecordingService.class);
        mServiceController.create().bind();
        mService = mServiceController.get();
        Assert.assertNotNull(mService);
        //get field of private mWorkerHandler，and shadow it's looper
        Field workerHandlerField = mService.getClass().getDeclaredField("mWorkerHandler");
        workerHandlerField.setAccessible(true);
        Handler workerHandler = (Handler) workerHandlerField.get(mService);
        assertNotNull(workerHandler);
        mShadowLooper = Shadows.shadowOf(workerHandler.getLooper());

        MemberModifier.field(RecorderActivity.class, "mService").set(recorderActivity, mService);

    }

    @Test
    public void testActivityLifecycle() throws Exception {
        assertNotNull(mController);
        mController.get().setTheme(R.style.Theme_SoundRecorder);
        mController.create();
        mController.start();
        mController.resume();
        mController.pause();
        mController.stop();
        mController.destroy();
    }

    @Test
    @Config(shadows = {ShadowSprdRecorder.class})
    public void testCreateRecord() throws Exception {
        TextView mStateMessage2 = recorderActivity.findViewById(R.id.stateMessage2);
        assertEquals(recorderActivity.getString(R.string.recording), mStateMessage2.getText());

        MemberModifier.field(RecorderActivity.class, "mNeedRequestPermissions").set(recorderActivity, false);
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        Assert.assertTrue(mStartOrPauseRecordBtn.hasOnClickListeners());
        mStartOrPauseRecordBtn.performClick();
        Field mRecorderListenerField = recorderActivity.getClass().getDeclaredField("mRecorderListener");
        mRecorderListenerField.setAccessible(true);
        RecordingService.RecorderListener mRecorderListener = (RecordingService.RecorderListener) mRecorderListenerField.get(recorderActivity);
        MemberModifier.field(mService.getClass(), "mRecorderListener").set(mService, mRecorderListener);
        mShadowLooper.runOneTask();

        assertEquals(recorderActivity.getString(R.string.soundrecording), mStateMessage2.getText());
    }

    @Test
    @Config(shadows = ShadowSprdRecorder.class)
    public void testPauseRecord() throws Exception {
        TextView mStateMessage2 = recorderActivity.findViewById(R.id.stateMessage2);
        assertEquals(recorderActivity.getString(R.string.recording), mStateMessage2.getText());

        recordState.mNowState = RecordingService.State.RECORDING_STATE;
        MemberModifier.field(RecorderActivity.class, "mNeedRequestPermissions").set(recorderActivity, false);
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        MemberModifier.field(RecordingService.class, "mState").set(mService, recordState);
        Field mRecorderListenerField = recorderActivity.getClass().getDeclaredField("mRecorderListener");
        mRecorderListenerField.setAccessible(true);
        RecordingService.RecorderListener mRecorderListener = (RecordingService.RecorderListener) mRecorderListenerField.get(recorderActivity);
        MemberModifier.field(mService.getClass(), "mRecorderListener").set(mService, mRecorderListener);
        mStartOrPauseRecordBtn.performClick();

        assertEquals(recorderActivity.getString(R.string.pauserecording), mStateMessage2.getText());
    }

    @Test
    @Config(shadows = ShadowSprdRecorder.class)
    public void testResumeRecord() throws Exception {
        TextView mStateMessage2 = recorderActivity.findViewById(R.id.stateMessage2);
        assertEquals(recorderActivity.getString(R.string.recording), mStateMessage2.getText());

        recordState.mNowState = RecordingService.State.SUSPENDED_STATE;
        MemberModifier.field(RecorderActivity.class, "mNeedRequestPermissions").set(recorderActivity, false);
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        MemberModifier.field(RecordingService.class, "mState").set(mService, recordState);
        Field mRecorderListenerField = recorderActivity.getClass().getDeclaredField("mRecorderListener");
        mRecorderListenerField.setAccessible(true);
        RecordingService.RecorderListener mRecorderListener = (RecordingService.RecorderListener) mRecorderListenerField.get(recorderActivity);
        MemberModifier.field(mService.getClass(), "mRecorderListener").set(mService, mRecorderListener);
        mStartOrPauseRecordBtn.performClick();

        assertEquals(recorderActivity.getString(R.string.soundrecording), mStateMessage2.getText());
    }

    @Test
    @Config(shadows = {ShadowSprdRecorder.class})
    public void testStopRecord() throws Exception {
        recordState.mNowState = RecordingService.State.RECORDING_STATE;
        MemberModifier.field(RecorderActivity.class, "mNeedRequestPermissions").set(recorderActivity, false);
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        MemberModifier.field(RecordingService.class, "mState").set(mService, recordState);
        assertNotNull(mStopAndListBtn);
        assertTrue(mStopAndListBtn.hasOnClickListeners());
        mStopAndListBtn.performClick();

        mShadowLooper.runOneTask();
        assertNotNull(ShadowToast.getLatestToast());
        assertEquals(recorderActivity.getString(R.string.recording_save), ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void testTagRecord() throws Exception {
        assertEquals("", mTagAndSettingBtn.getText());

        recordState.mNowState = RecordingService.State.RECORDING_STATE;
        MemberModifier.field(RecorderActivity.class, "mNeedRequestPermissions").set(recorderActivity, false);
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        MemberModifier.field(RecordingService.class, "mState").set(mService, recordState);
        assertNotNull(mTagAndSettingBtn);
        assertTrue(mTagAndSettingBtn.hasOnClickListeners());
        ShadowSystemClock.advanceBy(Duration.ofSeconds(2000));
        mTagAndSettingBtn.performClick();

        assertEquals("1", mTagAndSettingBtn.getText());
    }

    @Test
    @Config(shadows = {ShadowSprdRecorder.class})
    public void testShutDownBroadCase() throws Exception {
        recordState.mNowState = RecordingService.State.RECORDING_STATE;
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        MemberModifier.field(RecordingService.class, "mState").set(mService, recordState);

        Field mRecorderListenerField = recorderActivity.getClass().getDeclaredField("mRecorderListener");
        mRecorderListenerField.setAccessible(true);
        RecordingService.RecorderListener mRecorderListener = (RecordingService.RecorderListener) mRecorderListenerField.get(recorderActivity);
        MemberModifier.field(mService.getClass(), "mRecorderListener").set(mService, mRecorderListener);

        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        Intent intent = new Intent(Intent.ACTION_SHUTDOWN);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));
        Field mReceiverField = recorderActivity.getClass().getDeclaredField("mReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver mReceiver = (BroadcastReceiver) mReceiverField.get(recorderActivity);
        assertNotNull(mReceiver);
        mReceiver.onReceive(recorderActivity.getApplicationContext(), intent);
        mShadowLooper.runOneTask();

        TextView mStateMessage2 = recorderActivity.findViewById(R.id.stateMessage2);
        assertEquals(recorderActivity.getString(R.string.recording),mStateMessage2.getText());
    }

    @Test
    public void testStartRecordListActivity() throws Exception {
        recordState.mNowState = RecordingService.State.IDLE_STATE;
        MemberModifier.field(RecorderActivity.class, "mNeedRequestPermissions").set(recorderActivity, false);
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        assertNotNull(mStopAndListBtn);
        assertTrue(mStopAndListBtn.hasOnClickListeners());
        mStopAndListBtn.performClick();

        Intent exceptIntent = new Intent(recorderActivity, RecordListActivity.class);
        Intent actual = Shadows.shadowOf(RuntimeEnvironment.application)
                .getNextStartedActivity();
        assertEquals(exceptIntent.getComponent(), actual.getComponent());
    }

    @Test
    public void testStartSoundPickerActivity() throws Exception {
        recordState.mNowState = RecordingService.State.IDLE_STATE;
        MemberModifier.field(RecorderActivity.class, "mNeedRequestPermissions").set(recorderActivity, false);
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        MemberModifier.field(RecorderActivity.class, "isFromOtherApp").set(recorderActivity, true);
        assertNotNull(mStopAndListBtn);
        assertTrue(mStopAndListBtn.hasOnClickListeners());
        ShadowSystemClock.advanceBy(Duration.ofSeconds(2000));
        mStopAndListBtn.performClick();

        Intent exceptIntent = new Intent(recorderActivity, SoundPicker.class);
        Intent actual = Shadows.shadowOf(RuntimeEnvironment.application)
                .getNextStartedActivity();
        assertEquals(exceptIntent.getComponent(), actual.getComponent());
    }

    @Test
    public void testStartSettingActivity() throws Exception {
        recordState.mNowState = RecordingService.State.IDLE_STATE;
        MemberModifier.field(RecorderActivity.class, "mNeedRequestPermissions").set(recorderActivity, false);
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        assertNotNull(mTagAndSettingBtn);
        assertTrue(mTagAndSettingBtn.hasOnClickListeners());
        ShadowSystemClock.advanceBy(Duration.ofSeconds(2000));
        mTagAndSettingBtn.performClick();

        Intent exceptIntent = new Intent(recorderActivity, SettingActivity.class);
        exceptIntent.putExtra(SettingActivity.FROM_OTHER_APP, false);
        Intent actual = Shadows.shadowOf(RuntimeEnvironment.application)
                .getNextStartedActivity();
        assertEquals(exceptIntent.getComponent(), actual.getComponent());
    }

    @Test
    public void testCreatRecorderActivityFromOtherApp() throws Exception {
        Intent intent = new Intent(RuntimeEnvironment.application, RecorderActivity.class);
        intent.setType("audio/amr");
        mController = Robolectric.buildActivity(RecorderActivity.class, intent);
        recorderActivity = mController.create().get();
        Field mIsFromOtherAppField = recorderActivity.getClass().getDeclaredField("isFromOtherApp");
        mIsFromOtherAppField.setAccessible(true);
        boolean isFromOtherApp = (boolean) mIsFromOtherAppField.get(recorderActivity);
        assertTrue(isFromOtherApp);
    }

    @Test
    public void testCreatSettingActivityFromOtherApp() throws Exception {
        testCreatRecorderActivityFromOtherApp();

        recordState.mNowState = RecordingService.State.IDLE_STATE;
        MemberModifier.field(RecorderActivity.class, "mNeedRequestPermissions").set(recorderActivity, false);
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        mTagAndSettingBtn = recorderActivity.findViewById(R.id.tagButton);
        assertNotNull(mTagAndSettingBtn);
        assertTrue(mTagAndSettingBtn.hasOnClickListeners());
        ShadowSystemClock.advanceBy(Duration.ofSeconds(2000));
        mTagAndSettingBtn.performClick();

        Intent exceptIntent = new Intent(recorderActivity, SettingActivity.class);
        exceptIntent.putExtra(SettingActivity.FROM_OTHER_APP, false);
        Intent actual = Shadows.shadowOf(RuntimeEnvironment.application)
                .getNextStartedActivity();
        assertEquals(exceptIntent.getComponent(), actual.getComponent());
        assertTrue(actual.getBooleanExtra(SettingActivity.FROM_OTHER_APP, false));
    }

    @Test
    public void testMultiWindowModeChange() throws Exception {
        recorderActivity = mController.create().visible().get();
        assertFalse(recorderActivity.isInMultiWindowMode());
        recorderActivity.onMultiWindowModeChanged(true);
        assertNotNull(ShadowToast.getLatestToast());
        assertEquals("Sound Recorder does not support multi window mode, please use Sound Recorder in full screen mode."
                , ShadowToast.getTextOfLatestToast());
        assertTrue(recorderActivity.isFinishing());
    }

    @Test
    public void testPermission() throws Exception {
        Field beforeNeedPermissionsField = recorderActivity.getClass().getDeclaredField("mNeedRequestPermissions");
        beforeNeedPermissionsField.setAccessible(true);
        boolean beforeNeedPermissions = (boolean) beforeNeedPermissionsField.get(recorderActivity);
        assertTrue(beforeNeedPermissions);

        Shadows.shadowOf(RuntimeEnvironment.application).grantPermissions(permissions);
        mController = Robolectric.buildActivity(RecorderActivity.class);
        MemberModifier.field(recorderActivity.getClass(), "mServiceConnection").set(recorderActivity, serviceConnection);
        recorderActivity.onRequestPermissionsResult(200, permissions, new int[]{0, 0, 0});
        recorderActivity = mController.create().visible().get();

        Field afterNeedPermissionsField = recorderActivity.getClass().getDeclaredField("mNeedRequestPermissions");
        afterNeedPermissionsField.setAccessible(true);
        boolean afterNeedPermissions = (boolean) afterNeedPermissionsField.get(recorderActivity);
        assertFalse(afterNeedPermissions);
    }

    @Test
    public void testNoPermission() throws Exception {
        recorderActivity.onRequestPermissionsResult(200, permissions, new int[]{-1, -1, -1});
        AlertDialog alertDialog = ShadowAlertDialog.getLatestAlertDialog();
        assertNotNull(alertDialog);
        ShadowAlertDialog shadowAlertDialog = Shadows.shadowOf(alertDialog);
        assertEquals("The app does not have critical permissions needed to run. Please check your permissions settings.", shadowAlertDialog.getMessage());
        Button okButton = alertDialog.getButton(AlertDialog.BUTTON_POSITIVE);
        assertNotNull(okButton);
        okButton.performClick();
        assertTrue(recorderActivity.isFinishing());
    }

    @Test
    public void testKeyDownBackAtStartState() throws Exception {
        testCreatRecorderActivityFromOtherApp();
        recordState.mNowState = RecordingService.State.STARTING_STATE;
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        KeyEvent keyEvent = new KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_BACK);
        boolean flag = recorderActivity.onKeyDown(KeyEvent.KEYCODE_BACK, keyEvent);
        assertTrue(flag);
    }

    @Test
    public void testKeyDownBackAtRecordState() throws Exception {
        testCreatRecorderActivityFromOtherApp();
        recordState.mNowState = RecordingService.State.RECORDING_STATE;
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);
        MemberModifier.field(RecorderActivity.class, "mService").set(recorderActivity, mService);
        KeyEvent keyEvent = new KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_BACK);
        boolean flag = recorderActivity.onKeyDown(KeyEvent.KEYCODE_BACK, keyEvent);
        assertTrue(flag);
        assertTrue(recorderActivity.isFinishing());
    }

    @Test
    public void testChangeSuperSavingModel() throws Exception {
        recordState.mNowState = RecordingService.State.RECORDING_STATE;
        MemberModifier.field(RecorderActivity.class, "mRecordState").set(recorderActivity, recordState);

        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = "android.os.action.POWEREX_SAVE_MODE_CHANGED";
        Intent intent = new Intent(action);
        intent.putExtra("mode", 4);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = recorderActivity.getClass().getDeclaredField("mReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver mReceiver = (BroadcastReceiver) mReceiverField.get(recorderActivity);
        assertNotNull(mReceiver);
        mReceiver.onReceive(recorderActivity.getApplicationContext(), intent);
        assertTrue(RecorderActivity.mIsSupportSuperPowerSavingMode);
    }
}