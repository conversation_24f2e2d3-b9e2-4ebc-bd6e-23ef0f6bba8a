/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.os.storage.StorageManager;
import com.sprd.soundrecorder.frameworks.SprdFramewoks;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.io.File;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;

@Implements(SprdFramewoks.class)
public class ShadowSprdFrameworks {
    private static final String emulatedPathPrefix = "/storage/sdcard0";
    private static final String internalPathPrefix = "/storage/emulated/0";

    @Implementation
    public File getInternalStoragePath() {
        return new File(internalPathPrefix);
    }

    @Implementation
    public String getExternalStoragePathState() {
        return "mounted";
    }

    @Implementation
    public File getExternalStoragePath() {
        return new File(emulatedPathPrefix);
    }

    @Implementation
    public HashMap<String, String> getUsbVolumnInfo(StorageManager storageManager) {
        HashMap<String, String> result = new HashMap<>();
        return result;
    }

    @Implementation
    public File[] getUsbStoragePath() {
        return new File[]{};
    }


}
