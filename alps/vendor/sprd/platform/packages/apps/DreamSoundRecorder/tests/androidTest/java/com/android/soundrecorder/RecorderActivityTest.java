/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.android.soundrecorder;

import android.bluetooth.BluetoothProfile;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.filters.SmallTest;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.runner.AndroidJUnit4;
import androidx.test.uiautomator.*;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;


import static org.junit.Assert.*;

/**
 * perform condition:
 * 1.Home activity needs: settings, email, soundrecorder,phonecall
 * 2.The status bar needs: music player、screenrecorder
 * 3.Cmcc Sim card required
 */
@SmallTest
@RunWith(AndroidJUnit4.class)
public class RecorderActivityTest {

    private static final int LAUNCH_TIMEOUT = 500;
    private static final String mPackageName = "com.android.soundrecorder";
    private static final String mSettingPackageName = "com.android.settings";
    private static final String musicPackageName = "com.android.music";
    private UiDevice mDevice;
    private Context context;
    private VerifyToastUtil verifyToastUtil;

    @Before
    public void SetUp() {
        mDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());

        //Start from home
        mDevice.pressHome();
        // Wait for launcher
        final String launcherPackage = mDevice.getLauncherPackageName();
        Assert.assertNotNull(launcherPackage);
        mDevice.wait(Until.hasObject(By.pkg(launcherPackage).depth(0)), LAUNCH_TIMEOUT);
        // launch the app
        context = ApplicationProvider.getApplicationContext();
        final Intent intent = context.getPackageManager()
                .getLaunchIntentForPackage(mPackageName);
        context.startActivity(intent);
        // Wait for the app to appear
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);

        try {
            if (!mDevice.isScreenOn()) {
                mDevice.wakeUp();
                UiObject default_clock = mDevice.findObject(new UiSelector().resourceId("com.android.systemui:id/default_clock_view"));
                if (default_clock.exists()) {
                    mDevice.swipe(mDevice.getDisplayWidth() / 2, mDevice.getDisplayHeight() - 100, mDevice.getDisplayWidth() / 2, mDevice.getDisplayHeight() / 2, 5);
                }
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }

        UiObject premissionBtn = mDevice.findObject(new UiSelector().resourceId("com.android.permissioncontroller:id/permission_allow_button"));
        try {
            premissionBtn.click();
            premissionBtn.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
        }
        verifyToastUtil = new VerifyToastUtil();
        verifyToastUtil.monitoringToastMessage();
    }

    @Test
    public void testRecording() throws Exception {
        UiObject stateMessage = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stateMessage2"));
        Assert.assertNotNull(stateMessage);
        Assert.assertEquals(context.getString(R.string.recording), stateMessage.getText());
        //start recording
        UiObject startAndPauseButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recordButton"));
        Assert.assertNotNull(startAndPauseButton);
        startAndPauseButton.click();
        //wait for click startButton
        Thread.sleep(500);
        String startButtonDes = startAndPauseButton.getContentDescription();
        if (!context.getString(R.string.pause).equals(startButtonDes)) {
            return;
        }
        //add tag
        UiObject tagButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/tagButton"));
        Assert.assertNotNull(tagButton);
        tagButton.click();
        Thread.sleep(1000);
        tagButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/tagButton"));
        tagButton.click();
        //pause recording
        startAndPauseButton.click();
        Thread.sleep(500);
        Assert.assertEquals(context.getString(R.string.pauserecording), stateMessage.getText());
        tagButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/tagButton"));
        tagButton.click();
        //resume recording
        startAndPauseButton.click();
        Assert.assertEquals(context.getString(R.string.soundrecording), stateMessage.getText());
        //stop recording
        UiObject stopButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stopButton"));
        if (!stopButton.exists()) {
            mDevice.wakeUp();
            UiObject default_clock = mDevice.findObject(new UiSelector().resourceId("com.android.systemui:id/default_clock_view"));
            if (default_clock.exists()) {
                mDevice.swipe(mDevice.getDisplayWidth() / 2, mDevice.getDisplayHeight() - 100, mDevice.getDisplayWidth() / 2, mDevice.getDisplayHeight() / 2, 5);
            }
            stopButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stopButton"));
        }
        stopButton.click();
        try {
            mDevice.findObject(new UiSelector().text("SAVE")).click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
        }
        UiObject stateMessage2 = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stateMessage2"));
        Assert.assertNotNull(stateMessage2);
        Assert.assertEquals(context.getString(R.string.recording), stateMessage2.getText());
    }

    @Test
    public void testRecordingNoAuto() throws Exception {
        //start setting activity
        UiObject tagButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/tagButton"));
        Assert.assertNotNull(tagButton);
        tagButton.clickAndWaitForNewWindow();
        //select setting activity content view
        UiSelector listView = new UiSelector().className("android.widget.FrameLayout").instance(1)
                .childSelector(new UiSelector().className("android.widget.ListView"));
        //select auto save recording
        UiObject autoSavePreference = mDevice.findObject(listView.childSelector(new UiSelector()
                .className("android.widget.LinearLayout")
                .instance(2)));
        UiObject checkBoxObject = autoSavePreference.getChild(new UiSelector().resourceId("android:id/switch_widget"));
        if (checkBoxObject.isChecked()) {
            checkBoxObject.click();
            Assert.assertFalse(checkBoxObject.isChecked());
        }
        //go back recoder activity
        UiObject backButton = mDevice.findObject(new UiSelector().resourceId("android:id/action_bar")).getChild(new UiSelector().instance(0));
        backButton.click();
        //start recording
        UiObject startAndPauseButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recordButton"));
        Assert.assertNotNull(startAndPauseButton);
        startAndPauseButton.click();
        Thread.sleep(3000);
        /* Bug 1466025 testRecordingNoAuto case fail result in native crash @{ */
        String startButtonDes = startAndPauseButton.getContentDescription();
        if (!context.getString(R.string.pause).equals(startButtonDes)) {
            return;
        }
        /* Bug 1466025 }@ */
        //stop recording
        UiObject stopButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stopButton"));
        stopButton.click();
        mDevice.findObject(new UiSelector().text("SAVE")).click();
        Thread.sleep(2000);
        UiObject stateMessage = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stateMessage2"));
        Assert.assertNotNull(stateMessage);
        Assert.assertEquals(context.getString(R.string.recording), stateMessage.getText());

        //rest save type to no auto
        tagButton.clickAndWaitForNewWindow();
        checkBoxObject.click();
    }

    /**
     * perform condition: navigation bar have music
     *
     * @throws Exception
     */
//    @Test
    public void testLoseAudioFocusAtRecording() throws Exception {
        UiObject stateMessage = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stateMessage2"));
        Assert.assertNotNull(stateMessage);
        Assert.assertEquals(context.getString(R.string.recording), stateMessage.getText());
        //start recording
        UiObject startAndPauseButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recordButton"));
        Assert.assertNotNull(startAndPauseButton);
        startAndPauseButton.click();
        Thread.sleep(2000);
        String recordName = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/filename")).getText();
        System.out.println("recordName:" + recordName);

        //play music
        mDevice.swipe(300, 0, 300, 800, 50);
        mDevice.waitForIdle(1000);
        //if there is no music skip test
        try {
            UiObject musicButton = mDevice.findObject(new UiSelector().resourceId("com.android.music:id/toggle_btn"));
            musicButton.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            //todo stop recording
            //stop recording
            mDevice.findObject(new UiSelector().text("Sound Recorder")).clickAndWaitForNewWindow();
            Thread.sleep(500);
            mDevice.findObject(new UiSelector().text("STOP AND SAVE")).click();
            return;
        }

        mDevice.waitForIdle(1000);
        mDevice.swipe(300, 800, 300, 0, 50);
        mDevice.waitForIdle(1000);
        mDevice.swipe(300, 800, 300, 0, 50);
        mDevice.waitForIdle(1000);
        mDevice.swipe(300, 800, 300, 0, 50);

        //start recordListActivity
        UiObject stopButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stopButton"));
        Assert.assertNotNull(stopButton);
        stopButton.click();
        String displayName = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/record_displayname")).getText();
        System.out.println("displayName2:" + displayName);
        assertTrue(displayName.contains(recordName));
    }

    /**
     * perform condition: home page have setting app
     *
     * @throws Exception
     */
//    @Test
    public void testDeleteTmpRecord() throws Exception {
        //start recording
        UiObject startAndPauseButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recordButton"));
        Assert.assertNotNull(startAndPauseButton);
        startAndPauseButton.click();
        Thread.sleep(2000);
        String recordName = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/filename")).getText();
        System.out.println("recordName:" + recordName);

        mDevice.pressBack();
        //if home page has no setting app skip test
        try {
            mDevice.findObject(new UiSelector().text("Settings")).clickAndWaitForNewWindow();
            mDevice.swipe(300, 800, 300, 700, 50);
            mDevice.findObject(new UiSelector().text("Storage")).clickAndWaitForNewWindow();
//            mDevice.findObject(new UiSelector().text("内部共享存储空间")).click();

            mDevice.swipe(300, 800, 300, 400, 50);
            mDevice.findObject(new UiSelector().text("Files")).clickAndWaitForNewWindow();
            mDevice.findObject(new UiSelector().text("Android")).clickAndWaitForNewWindow();
            mDevice.findObject(new UiSelector().text("media")).clickAndWaitForNewWindow();
            mDevice.findObject(new UiSelector().text(mPackageName)).clickAndWaitForNewWindow();
            mDevice.findObject(new UiSelector().text("recordings")).clickAndWaitForNewWindow();

            mDevice.findObject(new UiSelector().textContains(recordName)).longClick();
            mDevice.findObject(new UiSelector().resourceId("com.android.documentsui:id/option_menu_search")).click();
            mDevice.findObject(new UiSelector().text("OK")).click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            //stop recording
            mDevice.swipe(300, 0, 300, 800, 50);
            mDevice.findObject(new UiSelector().text("Sound Recorder")).clickAndWaitForNewWindow();
            Thread.sleep(500);
            mDevice.findObject(new UiSelector().text("STOP AND SAVE")).click();
            return;
        }

        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        String renameMessage = verifyToastUtil.getToastMessage();
        Assert.assertEquals(context.getString(R.string.recording_nosave), renameMessage);

    }

    @Test
    public void testPauseOrResumeBroadcast() throws Exception {
        //start recording
        UiObject startAndPauseButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recordButton"));
        Assert.assertNotNull(startAndPauseButton);
        startAndPauseButton.click();
        /* Bug 1466025 testRecordingNoAuto case fail result in native crash @{ */
        Thread.sleep(1000);
        String startButtonDes = startAndPauseButton.getContentDescription();
        if (!context.getString(R.string.pause).equals(startButtonDes)) {
            return;
        }
        /* Bug 1466025 }@ */
        Thread.sleep(1000);
        mDevice.executeShellCommand("am broadcast -a com.android.soundrecorder.pause");
        Thread.sleep(1000);
        mDevice.executeShellCommand("am broadcast -a com.android.soundrecorder.pause");
        Thread.sleep(1000);
        mDevice.executeShellCommand("am broadcast -a com.android.soundrecorder.stop");
        UiObject stateMessage = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stateMessage2"));
        Assert.assertNotNull(stateMessage);
        Assert.assertEquals(context.getString(R.string.recording), stateMessage.getText());
    }

    /**
     * perform condition: navigation bar have screen record
     *
     * @throws Exception
     */
//    @Test
    public void testMicUsed() throws Exception {
        mDevice.swipe(300, 0, 300, 800, 50);
        mDevice.swipe(300, 0, 300, 800, 50);
        //start screen record(using mic)
        //if navigation bar has no screen record skip test
        try {
            UiObject screenRecord = mDevice.findObject(new UiSelector().text("Screen Record"));
            screenRecord.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        UiObject recordAudio = mDevice.findObject(new UiSelector().resourceId("com.android.systemui:id/screen_recording_options"));
        recordAudio.click();
        UiObject micPhone = mDevice.findObject(new UiSelector().text("Microphone"));
        micPhone.click();
        UiObject recordAudioSwitch = mDevice.findObject(new UiSelector().resourceId("com.android.systemui:id/screenrecord_audio_switch"));
        recordAudioSwitch.click();
        UiObject startButton = mDevice.findObject(new UiSelector().resourceId("com.android.systemui:id/button_start"));
        startButton.click();
        Thread.sleep(4500);
        //start recording
        UiObject startAndPauseButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recordButton"));
        Assert.assertNotNull(startAndPauseButton);
        startAndPauseButton.click();
        UiObject stateMessage = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stateMessage2"));
        Assert.assertNotNull(stateMessage);
        Assert.assertEquals(context.getString(R.string.recording), stateMessage.getText());

        //close screen recorder
        mDevice.swipe(300, 0, 300, 800, 50);
        UiObject screenRecord2 = mDevice.findObject(new UiSelector().text("Tap to stop"));
        screenRecord2.click();
        Thread.sleep(1000);
    }

    /**
     * perform condition: home page have call app
     *
     * @throws Exception
     */
    @Test
    public void testInCalling() throws Exception {
        mDevice.pressHome();
        //if home page has no phone app skip test
        try {
            UiObject phone = mDevice.findObject(new UiSelector().text("Phone"));
            phone.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        UiObject2 fab = mDevice.wait(Until.findObject(By.res("com.google.android.dialer", "dialpad_fab")), LAUNCH_TIMEOUT);
        if (fab == null) {
            fab = mDevice.wait(Until.findObject(By.res("com.android.dialer", "fab")), LAUNCH_TIMEOUT);
        }
        fab.click();
        UiObject2 digits = mDevice.wait(Until.findObject(By.res("com.google.android.dialer", "digits")), LAUNCH_TIMEOUT);
        if (digits == null) {
            digits = mDevice.wait(Until.findObject(By.res("com.android.dialer", "digits")), LAUNCH_TIMEOUT);
        }
        digits.setText("10086");
        UiObject2 callButton = mDevice.wait(Until.findObject(By.res("com.google.android.dialer", "dialpad_voice_call_button")), LAUNCH_TIMEOUT);
        if (callButton == null) {
            callButton = mDevice.wait(Until.findObject(By.res("com.android.dialer", "dialpad_floating_action_button")), LAUNCH_TIMEOUT);
        }
        callButton.clickAndWait(Until.newWindow(), LAUNCH_TIMEOUT);
        Thread.sleep(4000);
        mDevice.pressHome();
        try {
            UiObject soundRecorder = mDevice.findObject(new UiSelector().text("Sound Recorder"));
            soundRecorder.clickAndWaitForNewWindow();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        //start recording
        UiObject startAndPauseButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recordButton"));
        Assert.assertNotNull(startAndPauseButton);
        startAndPauseButton.click();
        UiObject stateMessage = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stateMessage2"));
        Assert.assertNotNull(stateMessage);
        Assert.assertEquals(context.getString(R.string.recording), stateMessage.getText());

        //hang up call
        mDevice.swipe(300, 0, 300, 800, 50);
        UiObject screenRecord2 = mDevice.findObject(new UiSelector().text("HANG UP"));
        screenRecord2.click();
        Thread.sleep(1000);
    }


    //    @Test
    public void testRecordingChangeSuperSavingModel() {
        try {
            UiObject stateMessage = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stateMessage2"));
            Assert.assertNotNull(stateMessage);
            Assert.assertEquals(context.getString(R.string.recording), stateMessage.getText());

            //start recording
            UiObject startAndPauseButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recordButton"));
            Assert.assertNotNull(startAndPauseButton);
            startAndPauseButton.click();
            //wait for click startButton
            mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
            Assert.assertEquals(context.getString(R.string.soundrecording), stateMessage.getText());

            UiObject uiRecordName1 = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/filename"));
            String recordName = uiRecordName1.getText();
            System.out.println("recordName:" + recordName);

            mDevice.swipe(300, 0, 300, 800, 50);
            mDevice.waitForIdle(500);

            mDevice.swipe(300, 0, 300, 800, 50);
            mDevice.waitForIdle(500);

            UiObject savingModes = mDevice.findObject(new UiSelector().textContains("saving mode"));
            savingModes.click();
            UiObject superSavingMode = mDevice.findObject(new UiSelector().textContains("Ultra saving mode"));
            superSavingMode.click();
            UiObject okButton = mDevice.findObject(new UiSelector().textContains("ok"));
            okButton.click();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
