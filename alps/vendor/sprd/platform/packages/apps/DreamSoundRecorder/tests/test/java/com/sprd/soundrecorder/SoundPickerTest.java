/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.app.Activity;
import android.content.*;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.DataSetObserver;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.net.Uri;
import android.provider.MediaStore;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TextView;
import com.android.soundrecorder.R;
import com.sprd.soundrecorder.frameworks.StandardFrameworks;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.support.membermodification.MemberModifier;
import org.robolectric.Robolectric;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.Shadows;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.fakes.RoboCursor;
import org.robolectric.shadow.api.Shadow;
import org.robolectric.shadows.*;
import org.robolectric.shadows.ShadowAudioManager;
import org.robolectric.shadows.util.DataSource;

import java.lang.reflect.Field;
import java.util.Arrays;

import static org.junit.Assert.*;
import static org.robolectric.Shadows.shadowOf;

@RunWith(RobolectricTestRunner.class)
@Config(shadows = {ShadowSprdFrameworks.class})
public class SoundPickerTest {
    private ActivityController<SoundPicker> mController;
    private SoundPicker mSoundPicker;
    private ListView mListView;
    private ShadowMediaPlayer shadowMediaPlayer;
    @Mock
    private MediaPlayer mediaPlayer;
    @Mock
    private AudioManager audioManager;

    private static final String COMPOSER = "FMSoundRecorder";

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        mediaPlayer = Shadow.newInstanceOf(MediaPlayer.class);
        ShadowMediaPlayer.MediaInfo mediaInfo = new ShadowMediaPlayer.MediaInfo();
        shadowMediaPlayer = Shadows.shadowOf(mediaPlayer);
        ShadowMediaPlayer.addMediaInfo(DataSource.toDataSource("content://media/external/audio/media/2"), mediaInfo);
        shadowMediaPlayer.setDataSource(DataSource.toDataSource("content://media/external/audio/media/2"));
        Object[][] datas = new Object[][]{
                {1, "0.mp3"},
                {2, "1.mp3"},
                {3, "2.mp3"}
        };
        setSoundData(datas);

        mController = Robolectric.buildActivity(SoundPicker.class);
        mSoundPicker = mController.get();
    }

    @Test
    public void testActivityLifecycle() throws Exception {
        mController.create();
        mController.start();
        mController.visible();
        mController.resume();
        mController.pause();
        mController.stop();
        mController.destroy();
    }

    //    @Test
    public void testPlay() throws Exception {
        mSoundPicker = mController.create().start().visible().resume().get();
        MemberModifier.field(mSoundPicker.getClass(), "mMediaPlayer")
                .set(mSoundPicker, mediaPlayer);
        MemberModifier.field(mSoundPicker.getClass(), "mAudioManager")
                .set(mSoundPicker, audioManager);
        mListView = mSoundPicker.getListView();
        mListView.performItemClick(mSoundPicker.getListView(), 1, 1);
        assertNotNull(shadowMediaPlayer.getState());
        assertEquals("STARTED", shadowMediaPlayer.getState().toString());
    }

    //    @Test
    public void testStop() throws Exception {
        testPlay();
        assertEquals("STARTED", shadowMediaPlayer.getState().toString());
        mListView.performItemClick(mSoundPicker.getListView(), 1, 1);
        assertNotNull(shadowMediaPlayer.getState());
        assertEquals("END", shadowMediaPlayer.getState().toString());
    }

    //    @Test
    public void testSelectedRecord() throws Exception {
        testPlay();
        Button okButton = mSoundPicker.findViewById(R.id.btnEnsure);
        assertNotNull(okButton);
        assertTrue(okButton.hasOnClickListeners());
        okButton.performClick();
        ShadowActivity shadowActivity = Shadows.shadowOf(mSoundPicker);
        assertTrue(shadowActivity.isFinishing());
        assertEquals(Activity.RESULT_OK, shadowActivity.getResultCode());
    }

    @Test
    public void testBroadcastReceiver() throws Exception {
        mController.create().visible().get();
        TextView emptyList = mSoundPicker.findViewById(R.id.picker_emptylist);
        assertEquals(View.GONE, emptyList.getVisibility());

        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = Intent.ACTION_MEDIA_REMOVED;
        Intent intent = new Intent();
        intent.setAction(action);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));
        Field mReceiverField = mSoundPicker.getClass().getDeclaredField("mExternalMountedReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver receiver = (BroadcastReceiver) mReceiverField.get(mSoundPicker);
        assertNotNull(receiver);

        Object[][] datas = new Object[][]{};
        setSoundData(datas);
        receiver.onReceive(RuntimeEnvironment.application, intent);
        assertEquals(View.VISIBLE, emptyList.getVisibility());
    }

    //    @Test
    public void testCompletion() throws Exception {
        testPlay();
        assertEquals("STARTED", shadowMediaPlayer.getState().toString());
        mSoundPicker.onCompletion(mediaPlayer);
        assertNotNull(shadowMediaPlayer.getState());
        assertEquals("END", shadowMediaPlayer.getState().toString());
    }

    @Test
    public void testKeyDownBack() {
        mSoundPicker = mController.create().visible().get();
        KeyEvent keyEvent = new KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_BACK);
        boolean flag = mSoundPicker.onKeyDown(KeyEvent.KEYCODE_BACK, keyEvent);
        assertTrue(flag);
        assertTrue(mSoundPicker.isFinishing());
    }

    //    @Test
    public void testLossAudioFocus() throws Exception {
        testPlay();
        assertEquals("STARTED", shadowMediaPlayer.getState().toString());

        Field mAudioFocusListenerField = mSoundPicker.getClass().getDeclaredField("mAudioFocusListener");
        mAudioFocusListenerField.setAccessible(true);
        AudioManager.OnAudioFocusChangeListener audioFocusChangeListener = (AudioManager.OnAudioFocusChangeListener) mAudioFocusListenerField.get(mSoundPicker);
        assertNotNull(audioFocusChangeListener);

        audioFocusChangeListener.onAudioFocusChange(AudioManager.AUDIOFOCUS_LOSS);
        assertEquals("END", shadowMediaPlayer.getState().toString());
    }

    @Test
    public void testMultiWindowModeChange() throws Exception {
        mSoundPicker = mController.create().visible().get();
        assertFalse(mSoundPicker.isInMultiWindowMode());
        mSoundPicker.onMultiWindowModeChanged(true);
        assertNotNull(ShadowToast.getLatestToast());
        assertEquals("Sound Recorder does not support multi window mode, please use Sound Recorder in full screen mode."
                , ShadowToast.getTextOfLatestToast());
        assertTrue(mSoundPicker.isFinishing());
    }

    @Test
    public void testCancelSoundPicker() throws Exception {
        mSoundPicker = mController.create().visible().get();
        Button cancelButton = mSoundPicker.findViewById(R.id.btnCancel);
        assertNotNull(cancelButton);
        assertTrue(cancelButton.hasOnClickListeners());
        cancelButton.performClick();
        assertTrue(mSoundPicker.isFinishing());
    }

    private void setSoundData(Object[][] datas) {
        RoboCursor cursor = new RoboCursor() {
            @Override
            public boolean isClosed() {
                return true;
            }
        };
        cursor.setColumnNames(Arrays.asList(
                MediaStore.Audio.Media._ID,
                MediaStore.Audio.Media.DISPLAY_NAME));
        cursor.setResults(datas);
        shadowOf(RuntimeEnvironment.application.getContentResolver())
                .setCursor(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, cursor);
    }

}