/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.telephony.TelephonyManager;
import com.sprd.soundrecorder.frameworks.StandardFrameworks;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(StandardFrameworks.class)
public class ShadowStandardFrameworks {

    @Implementation
    public boolean getTeleHasIccCard(TelephonyManager tm, int slotId) {
        return true;
    }

}
