/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import com.sprd.soundrecorder.service.SprdRecorder;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(SprdRecorder.class)
public class ShadowSprdRecorder {

    @Implementation
    public boolean checkStorageSize(String selectPath) {
        return true;
    }

    @Implementation
    public SprdRecorder.ErrorInfo stopRecording(boolean isError) {
        return SprdRecorder.ErrorInfo.NO_ERROR;
    }

    @Implementation
    public SprdRecorder.ErrorInfo startRecording(String requestType) {
        return SprdRecorder.ErrorInfo.NO_ERROR;
    }

    @Implementation
    public SprdRecorder.ErrorInfo pauseRecording() {
        return SprdRecorder.ErrorInfo.NO_ERROR;
    }

    @Implementation
    public SprdRecorder.ErrorInfo resumeRecording() {
        return SprdRecorder.ErrorInfo.NO_ERROR;
    }
}
