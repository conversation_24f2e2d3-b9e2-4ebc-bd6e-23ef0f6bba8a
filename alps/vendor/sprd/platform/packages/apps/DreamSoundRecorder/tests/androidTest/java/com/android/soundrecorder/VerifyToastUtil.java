/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.android.soundrecorder;

import android.app.Notification;
import android.app.UiAutomation;
import android.os.Parcelable;
import android.view.accessibility.AccessibilityEvent;
import androidx.test.platform.app.InstrumentationRegistry;

/**
 * Capture the Toast that appears at runtime
 */
public class VerifyToastUtil {


    private String toastMessage;

    public String getToastMessage() {
        return toastMessage;
    }


    public  void monitoringToastMessage() {
        UiAutomation uiAutomation = InstrumentationRegistry.getInstrumentation().getUiAutomation();
        uiAutomation.setOnAccessibilityEventListener(new UiAutomation.OnAccessibilityEventListener() {
            @Override
            public void onAccessibilityEvent(AccessibilityEvent event) {
                if (event.getEventType() == AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED) {
                    try {
                        Parcelable parcelable = event.getParcelableData();
                        if (!(parcelable instanceof Notification)) {
                            toastMessage = (String) event.getText().get(0);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }

}
