/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder.service;

import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;
import androidx.test.internal.util.ReflectionUtil;
import com.google.common.reflect.Reflection;
import com.sprd.soundrecorder.*;
import org.apache.maven.artifact.ant.shaded.ReflectionUtils;
import org.apache.tools.ant.taskdefs.Sleep;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.powermock.api.support.membermodification.MemberModifier;
import org.robolectric.Robolectric;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.Shadows;
import org.robolectric.android.controller.ServiceController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLooper;

import java.lang.reflect.Field;

import static org.junit.Assert.*;

@RunWith(RobolectricTestRunner.class)
@Config(shadows = {ShadowSprdFrameworks.class})
public class RecordingServiceTest {
    private ServiceController<RecordingService> mServiceController;
    private RecordingService mService;
    private RecordingService.RecordState mState = new RecordingService.RecordState(RecordingService.State.IDLE_STATE, RecordingService.State.IDLE_STATE);
    private ShadowLooper mShadowLooper;

    @Before
    public void setUp() throws Exception {
        mServiceController = Robolectric.buildService(RecordingService.class);
        mService = mServiceController.get();
        Assert.assertNotNull(mService);
        mServiceController.create();

        //获取私有字段mWorkerHandler后，通过运行shadow的looper，回调handlMessage
        Field workerHandlerField = mService.getClass().getDeclaredField("mWorkerHandler");
        workerHandlerField.setAccessible(true);
        Handler workerHandler = (Handler) workerHandlerField.get(mService);
        assertNotNull(workerHandler);
        mShadowLooper = Shadows.shadowOf(workerHandler.getLooper());
    }

    @Test
    public void testServiceLifecycle() {
        mServiceController.create();
        mServiceController.startCommand(0, 0);
        mServiceController.bind();
        mServiceController.unbind();
        mServiceController.destroy();
    }

    @Test
    @Config(shadows = {ShadowSprdRecorder.class})
    public void testStartAndrPauseAndStopRecording() throws Exception {
        mService.startRecording("audio/3gpp", -1L);
        mShadowLooper.runOneTask();
        ShadowLooper.runMainLooperOneTask();

        mState.mNowState = RecordingService.State.RECORDING_STATE;
        MemberModifier.field(RecordingService.class, "mState").set(mService, mState);
        mService.pauseRecord();

        Field mRecorder = mService.getClass().getDeclaredField("mRecorder");
        mRecorder.setAccessible(true);
        SprdRecorder sprdRecorder = (SprdRecorder) mRecorder.get(mService);
        MemberModifier.field(SprdRecorder.class, "mRecordDuration").set(sprdRecorder, 5000L);

        mService.stopRecord();
        mShadowLooper.runOneTask();
        ShadowLooper.runMainLooperOneTask();
        //stop with new name
        mService.stopRecord("new name");
        //stop sync
        mService.doStopRecordSync();
    }

    @Test
    @Config(shadows = {ShadowSprdRecorder.class})
    public void testStopBySuperSavingModel() throws Exception {
        mState.mNowState = RecordingService.State.RECORDING_STATE;
        MemberModifier.field(RecordingService.class, "mState").set(mService, mState);
        Field mRecorder = mService.getClass().getDeclaredField("mRecorder");
        mRecorder.setAccessible(true);
        SprdRecorder sprdRecorder = (SprdRecorder) mRecorder.get(mService);
        MemberModifier.field(SprdRecorder.class, "mRecordDuration").set(sprdRecorder, 5000L);
        RecorderActivity.mIsSupportSuperPowerSavingMode =true;
        assertTrue(RecorderActivity.mIsSupportSuperPowerSavingMode);
        mService.stopRecord();
        mShadowLooper.runOneTask();
        ShadowLooper.runMainLooperOneTask();
    }
}