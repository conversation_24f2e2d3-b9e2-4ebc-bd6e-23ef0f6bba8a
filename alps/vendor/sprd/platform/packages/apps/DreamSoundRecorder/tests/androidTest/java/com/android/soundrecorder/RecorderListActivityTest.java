/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.android.soundrecorder;

import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.runner.AndroidJUnit4;
import androidx.test.uiautomator.*;
import com.sprd.soundrecorder.RecordListActivity;
import com.sprd.soundrecorder.RecorderActivity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import static org.junit.Assert.assertNotNull;

/**
 * perform condition:
 * 1.Home activity needs: settings, email, soundrecorder,phonecall
 * 2.The status bar needs: music player、screenrecorder
 * 3.Cmcc Sim card required
 */
//@RunWith(AndroidJUnit4.class)
public class RecorderListActivityTest {

    private static final int a = 0;
    private static final int LAUNCH_TIMEOUT = 500;
    private static final String mPackageName = "com.android.soundrecorder";
    private UiDevice mDevice;
    private Context context;

    //@Before
    public void SetUp() throws Exception {
        mDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());

        try {
            if (!mDevice.isScreenOn()) {
                mDevice.wakeUp();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        //Start from home
        mDevice.pressHome();
        // Wait for launcher
        final String launcherPackage = mDevice.getLauncherPackageName();
        Assert.assertNotNull(launcherPackage);
        mDevice.wait(Until.hasObject(By.pkg(launcherPackage).depth(0)), LAUNCH_TIMEOUT);
        // launch the app
        context = ApplicationProvider.getApplicationContext();
        final Intent intent = context.getPackageManager()
                .getLaunchIntentForPackage(mPackageName);
        context.startActivity(intent);
        // Wait for the app to appear
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);

        UiObject premissionBtn = mDevice.findObject(new UiSelector().resourceId("com.android.permissioncontroller:id/permission_allow_button"));
        try {
            premissionBtn.click();
            premissionBtn.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
        }
        //start recordListActivity
        UiObject stopButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stopButton"));
        Assert.assertNotNull(stopButton);
        stopButton.click();
    }

    /**
     * test RecordListActivity
     *
     * @throws Exception
     */
    //@Test
    public void testRecordListPlay() throws Exception {
        //play recording
        UiObject startRecordBtn = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recode_icon"));
        Assert.assertNotNull(startRecordBtn);
        startRecordBtn.click();
        UiObject receiverModeMenu = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/setting"));
        Assert.assertNotNull(receiverModeMenu);
        //receiver mode on
        receiverModeMenu.click();
        //stop playing recording
        startRecordBtn.click();
        //receiver mode off
        receiverModeMenu.click();
    }


    //@Test
    public void testLocalRecordList() throws Exception {
        UiObject table = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/tabs"));
        UiObject localTable = table.getChild(new UiSelector().className("android.widget.TextView").instance(0));
        Assert.assertEquals(context.getString(R.string.local_record), localTable.getText());
        UiObject listView = mDevice.findObject(new UiSelector().resourceId("android:id/list"));
        int itemCount = listView.getChildCount();
        if (itemCount == 0) {
            UiObject emptyListView = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/emptylist"));
            Assert.assertEquals(context.getString(R.string.emptylist), emptyListView.getText());
        }
    }

    //@Test
    public void testCallRecordList() throws Exception {
        testRecordListSwipe();
        UiObject listView = mDevice.findObject(new UiSelector().resourceId("android:id/list"));
        int itemCount = listView.getChildCount();
        if (itemCount == 0) {
            UiObject emptyListView = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/emptylist"));
            Assert.assertEquals(context.getString(R.string.emptylist), emptyListView.getText());
        }
    }

//    @Test
    public void testRecordListSwipe() throws Exception {
        UiObject table = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/tabs"));
        UiObject localTable = table.getChild(new UiSelector().className("android.widget.TextView").instance(0));
        UiObject callTable = table.getChild(new UiSelector().className("android.widget.TextView").instance(1));
        Assert.assertEquals(context.getString(R.string.local_record), localTable.getText());
        Assert.assertEquals(context.getString(R.string.call_record), callTable.getText());
        Assert.assertTrue(localTable.isSelected());
        Assert.assertFalse(callTable.isSelected());
        //swipe left to call recording
        UiObject listView = mDevice.findObject(new UiSelector().resourceId("android:id/list"));
        listView.swipeLeft(50);
        Assert.assertFalse(localTable.isSelected());
        Assert.assertTrue(callTable.isSelected());
    }

    //@Test
    public void testGoback() throws Exception {
        //play recording
        UiObject startRecordBtn = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recode_icon"));
        Assert.assertNotNull(startRecordBtn);
        startRecordBtn.click();
        UiObject backButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/id_toolbar")).getChild(new UiSelector().instance(0));
        backButton.click();
        Thread.sleep(1000);
        backButton.click();
        Thread.sleep(1000);
        //back to recorderActivity
        UiObject stateMessage = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stateMessage2"));
        Assert.assertNotNull(stateMessage);
        Assert.assertEquals(context.getString(R.string.recording), stateMessage.getText());
    }

    //@Test
    public void testGoback2() throws Exception {
        //play recording
        UiObject startRecordBtn = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recode_icon"));
        Assert.assertNotNull(startRecordBtn);
        startRecordBtn.click();
        mDevice.pressBack();
        Thread.sleep(1000);
        mDevice.pressBack();
        //back to recorderActivity
        UiObject stateMessage = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stateMessage2"));
        Assert.assertNotNull(stateMessage);
        Assert.assertEquals(context.getString(R.string.recording), stateMessage.getText());
    }

    //@Test
    public void testHeadPauseBroadcast() throws Exception {
        mDevice.executeShellCommand("am broadcast -a com.android.soundrecorder.HEADSET.PAUSE --es recorder_type 0");
        Thread.sleep(1000);
        //play recording
        UiObject startRecordBtn = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recode_icon"));
        Assert.assertNotNull(startRecordBtn);
        startRecordBtn.click();
        //pause recording
        Thread.sleep(2000);
        mDevice.executeShellCommand("am broadcast -a com.android.soundrecorder.HEADSET.PAUSE --es recorder_type 0");
        Assert.assertNotNull(startRecordBtn);
        Thread.sleep(2000);
        mDevice.executeShellCommand("am broadcast -a com.android.soundrecorder.HEADSET.PAUSE --es recorder_type 0");
        Assert.assertNotNull(startRecordBtn);
        Thread.sleep(2000);
    }

    //@Test
    public void testStatusPhoneBroadcast() throws Exception {
        mDevice.waitForWindowUpdate(mPackageName, 2000);
        mDevice.executeShellCommand("am broadcast -a android.intent.action.PHONE_STATE");
        Thread.sleep(1000);
        //play recording
        UiObject startRecordBtn = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recode_icon"));
        Assert.assertNotNull(startRecordBtn);
        startRecordBtn.click();
        mDevice.executeShellCommand("am broadcast -a android.intent.action.PHONE_STATE");
        Thread.sleep(2000);
    }

    /**
     * perform condition: navigation bar have music
     * @throws Exception
     */
    //@Test
    public void testPauseByLoseAudio() throws Exception {
        //play recording
        UiObject startRecordBtn = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/recode_icon"));
        Assert.assertNotNull(startRecordBtn);
        startRecordBtn.click();
        //play music
        mDevice.swipe(300, 0, 300, 800, 50);
        mDevice.waitForIdle(1000);
        //if there is no music skip test
        try {
            UiObject musicButton = mDevice.findObject(new UiSelector().resourceId("com.android.music:id/toggle_btn"));
            musicButton.click();
        }catch (UiObjectNotFoundException e){
            e.printStackTrace();
            return;
        }
        mDevice.waitForIdle(1000);
        mDevice.swipe(300, 800, 300, 0, 50);
        mDevice.waitForIdle(1000);
        mDevice.swipe(300, 800, 300, 0, 50);
        mDevice.waitForIdle(1000);
        mDevice.swipe(300, 800, 300, 0, 50);

        UiObject receiverModeMenu = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/setting"));
        Assert.assertNotNull(receiverModeMenu);
    }

}
