/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.android.soundrecorder;

import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;
import android.widget.Button;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.runner.AndroidJUnit4;
import androidx.test.uiautomator.*;
import com.sprd.soundrecorder.SettingActivity;
import com.sprd.soundrecorder.service.SprdRecorder;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.Random;

/**
 * perform condition:
 * 1.Home activity needs: settings, email, soundrecorder,phonecall
 * 2.The status bar needs: music player、screenrecorder
 * 3.Cmcc Sim card required
 */
@RunWith(AndroidJUnit4.class)
public class SettingActivityTest {

    private static final int LAUNCH_TIMEOUT = 500;
    private static final String mPackageName = "com.android.soundrecorder";
    private UiDevice mDevice;
    private UiSelector listView;
    private Context context;

    @Before
    public void SetUp() {
        try {

            mDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());
            try {
                if (!mDevice.isScreenOn()) {
                    mDevice.wakeUp();
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
            //Start from home
            mDevice.pressHome();
            // Wait for launcher
            final String launcherPackage = mDevice.getLauncherPackageName();
            Assert.assertNotNull(launcherPackage);
            mDevice.wait(Until.hasObject(By.pkg(launcherPackage).depth(0)), LAUNCH_TIMEOUT);
            // launch the app
            context = ApplicationProvider.getApplicationContext();
            final Intent intent = context.getPackageManager()
                    .getLaunchIntentForPackage(mPackageName);
            context.startActivity(intent);
            // Wait for the app to appear
            mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
            UiObject premissionBtn = mDevice.findObject(new UiSelector().resourceId("com.android.permissioncontroller:id/permission_allow_button"));
            try {
                premissionBtn.click();
                premissionBtn.click();
            } catch (UiObjectNotFoundException e) {
                e.printStackTrace();
            }
            UiObject tagButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/tagButton"));
            Assert.assertNotNull(tagButton);
            tagButton.clickAndWaitForNewWindow();
            //select setting activity content view
            listView = new UiSelector()
                    .className("android.widget.FrameLayout")
                    .instance(1)
                    .childSelector(new UiSelector().className("android.widget.ListView"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * test SettingActivity
     *
     * @throws Exception
     */
    @Test
    public void testSetType() throws Exception {
        UiObject typePreference = mDevice.findObject(listView.childSelector(new UiSelector()
                .className("android.widget.LinearLayout")
                .instance(0)));
        typePreference.click();
        //select aacType(instance : 0 mp3 ,1 aac , 2 amr)
        UiCollection typeList = new UiCollection(new UiSelector().className("android.widget.ListView"));
        int exceptTypeNum = new Random().nextInt(2);
        typeList.getChildByInstance(new UiSelector()
                .className("android.widget.CheckedTextView"), exceptTypeNum)
                .click();
        UiObject typeSummary = typePreference.getChild(new UiSelector().resourceId("android:id/summary"));
        String[] entries = SprdRecorder.getSupportRecordTypeString(context);
        Assert.assertEquals(typeSummary.getText(), entries[exceptTypeNum]);
    }

    @Test
    public void testSetPath() throws Exception {
        UiObject pathPreference = mDevice.findObject(listView.childSelector(new UiSelector()
                .className("android.widget.LinearLayout")
                .instance(1)));
        pathPreference.click();
        // select save path(instance : 0 手机 , 1 SD卡)
        UiCollection savePathList = new UiCollection(new UiSelector().className("android.widget.ListView"));
        savePathList.getChildByInstance(new UiSelector()
                .className("android.widget.CheckedTextView"), 0)
                .click();
        UiObject pathSummary = pathPreference.getChild(new UiSelector().resourceId("android:id/summary"));
        ArrayList<String> entries = SettingActivity.getCurrentSavePathEntries(context)[0];
        Assert.assertEquals(pathSummary.getText(), entries.get(0));
    }

    @Test
    public void testSetAutoSave() throws Exception {
        //select auto save recording
        UiObject autoSavePreference = mDevice.findObject(listView.childSelector(new UiSelector()
                .className("android.widget.LinearLayout")
                .instance(2)));
        UiObject checkBoxObject = autoSavePreference.getChild(new UiSelector().resourceId("android:id/switch_widget"));
        boolean beforeCheck = checkBoxObject.isChecked();
        autoSavePreference.click();
        boolean afterCheck = checkBoxObject.isChecked();
        if (beforeCheck)
            Assert.assertFalse(afterCheck);
        else
            Assert.assertTrue(afterCheck);
    }

    @Test
    public void testSetTimer() throws Exception {
        UiObject timerPreference = mDevice.findObject(listView.childSelector(new UiSelector()
                .className("android.widget.LinearLayout")
                .instance(4)));
        UiObject checkBoxObject = timerPreference.getChild(new UiSelector().resourceId("android:id/switch_widget"));
        boolean beforeCheck = checkBoxObject.isChecked();
        timerPreference.clickAndWaitForNewWindow();
        mDevice.findObject(new UiSelector().resourceId("android:id/button1")).click();
        checkBoxObject.click();
        Assert.assertFalse(checkBoxObject.isChecked());
        //back to home
        mDevice.pressHome();
        mDevice.swipe(300, 0, 300, 800, 50);
        mDevice.waitForIdle(1000);
    }

    @Test
    public void testCloseTimerBroadcast() throws Exception {
        UiObject timerPreference = mDevice.findObject(listView.childSelector(new UiSelector()
                .className("android.widget.LinearLayout")
                .instance(4)));
        timerPreference.clickAndWaitForNewWindow();
        mDevice.findObject(new UiSelector().resourceId("android:id/button1")).click();
        UiObject checkBoxObject = timerPreference.getChild(new UiSelector().resourceId("android:id/switch_widget"));
        boolean beforeCheck = checkBoxObject.isChecked();
        Assert.assertTrue(beforeCheck);
        mDevice.executeShellCommand("am broadcast -a com.android.soundrecorder.closetimer");
        boolean afterCheck = checkBoxObject.isChecked();
        Assert.assertFalse(afterCheck);
    }

//    @Test
    public void testCloseMissTimerBroadcast() throws Exception {
        UiObject timerPreference = mDevice.findObject(listView.childSelector(new UiSelector()
                .className("android.widget.LinearLayout")
                .instance(4)));
        UiObject checkBoxObject = timerPreference.getChild(new UiSelector().resourceId("android:id/switch_widget"));
        if (checkBoxObject.isChecked()) {
            checkBoxObject.click();
        }
        timerPreference.clickAndWaitForNewWindow();
        mDevice.findObject(new UiSelector().resourceId("android:id/button1")).click();
        mDevice.swipe(300, 0, 300, 800, 50);
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        UiObject tab = mDevice.findObject(new UiSelector().text("Sound Recorder"));
        Assert.assertNotNull(tab);
        mDevice.executeShellCommand("am broadcast -a com.android.soundrecorder.closemisstimer");
        Thread.sleep(1000);
        UiObject tab2 = mDevice.findObject(new UiSelector().text("Sound Recorder"));
        Assert.assertFalse(tab2.exists());
        Thread.sleep(1000);
    }


}
