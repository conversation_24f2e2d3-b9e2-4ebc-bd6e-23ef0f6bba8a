/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.content.Context;
import android.os.SystemClock;
import com.sprd.soundrecorder.data.DataOpration;
import com.sprd.soundrecorder.data.RecorderItem;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.ArrayList;

@Implements(DataOpration.class)
public class ShadowDataOpration {

    @Implementation
    public static ArrayList<RecorderItem> getRecorderData(final Context context, final boolean isCallData) {
        ArrayList<RecorderItem> recorderItems = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            RecorderItem item = new RecorderItem();
            item.setId(i);
            item.setData("/storage/emulated/0/Android/media/com.android.soundrecorder/recordings/"+i+".mp3");
            item.setSize(2000);
            item.setTitle("name" + i);
            item.setDisplayName(i+".mp3");
            item.setDateModify(SystemClock.currentThreadTimeMillis());
            item.setMimeType("audio/3gpp");
            item.setDuration(5000);
            item.setTagNumber(i);
            recorderItems.add(item);
        }
        return recorderItems;
    }

}
