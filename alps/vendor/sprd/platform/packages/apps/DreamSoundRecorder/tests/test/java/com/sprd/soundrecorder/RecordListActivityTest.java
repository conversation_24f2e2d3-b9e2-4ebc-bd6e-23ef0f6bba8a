/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.Manifest;
import android.app.AlertDialog;
import android.bluetooth.BluetoothHeadset;
import android.bluetooth.BluetoothProfile;
import android.content.BroadcastReceiver;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.view.*;
import android.widget.Button;
import android.widget.ImageButton;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Lifecycle;
import androidx.viewpager.widget.ViewPager;
import com.android.soundrecorder.R;
import com.google.common.collect.Lists;
import com.sprd.soundrecorder.ui.RecordItemFragment;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.robolectric.Robolectric;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.Shadows;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowAlertDialog;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowToast;

import java.lang.reflect.Field;
import java.util.ArrayList;

import static org.junit.Assert.*;

@RunWith(RobolectricTestRunner.class)
@Config(shadows = {ShadowSprdFrameworks.class})
public class RecordListActivityTest {
    private ActivityController<RecordListActivity> mController;
    private RecordListActivity recordListActivity;
    private String[] permissions = {Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_PHONE_STATE};


    @Before
    public void setUp() throws Exception {
        mController = Robolectric.buildActivity(RecordListActivity.class);
        recordListActivity = mController.get();
    }

    @Test
    public void testActivityLifecycle() {
        mController.get().setTheme(R.style.AppTheme);
        mController.create();
        Lifecycle lifecycle = recordListActivity.getLifecycle();
        assertEquals("CREATED", lifecycle.getCurrentState().name());
        mController.start();
        assertEquals("STARTED", lifecycle.getCurrentState().name());
        mController.resume();
        assertEquals("RESUMED", lifecycle.getCurrentState().name());
        mController.stop();
        mController.pause();
        System.out.println("life:"+lifecycle.getCurrentState().name());
        mController.destroy();
        assertEquals("DESTROYED", lifecycle.getCurrentState().name());
    }

    @Test
    public void testPageView() {
        mController.create().visible();
        ViewPager viewPager = recordListActivity.findViewById(R.id.id_page_vp);
        viewPager.setCurrentItem(0);
        assertEquals(0, viewPager.getCurrentItem());
        TestOnPageChangeListener listener = new TestOnPageChangeListener();
        viewPager.setOnPageChangeListener(listener);
        assertFalse(listener.onPageSelectedCalled);
        viewPager.setCurrentItem(1, true);
        assertTrue(listener.onPageSelectedCalled);
    }

    @Test
    @Config(shadows = {ShadowAudioManager.class})
    public void testHeadOn() throws Exception {
        mController.create().visible();
        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = Intent.ACTION_HEADSET_PLUG;
        Intent intent = new Intent(action);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = recordListActivity.getClass().getDeclaredField("mHeadsetReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver headReceiver = (BroadcastReceiver) mReceiverField.get(recordListActivity);
        assertNotNull(headReceiver);

        PowerMockito.field(RecordListActivity.class, "isReceiveMode").set(recordListActivity, true);
        headReceiver.onReceive(recordListActivity, intent);

        assertNotNull(ShadowToast.getLatestToast());
        assertEquals("Receiver mode is off", ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void testHeadOff() throws Exception {
        mController.create().visible();
        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = Intent.ACTION_HEADSET_PLUG;
        Intent intent = new Intent(action);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = recordListActivity.getClass().getDeclaredField("mHeadsetReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver headReceiver = (BroadcastReceiver) mReceiverField.get(recordListActivity);
        assertNotNull(headReceiver);

        headReceiver.onReceive(recordListActivity, intent);

    }

    @Test
    public void testBlueHeadOn() throws Exception {
        mController.create().visible();
        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED;
        Intent intent = new Intent(action);
        intent.putExtra(BluetoothProfile.EXTRA_STATE, BluetoothProfile.STATE_CONNECTED);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = recordListActivity.getClass().getDeclaredField("mHeadsetReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver headReceiver = (BroadcastReceiver) mReceiverField.get(recordListActivity);
        assertNotNull(headReceiver);
        headReceiver.onReceive(recordListActivity, intent);
    }

    @Test
    public void testBlueHeadOff() throws Exception {
        mController.create().visible();
        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED;
        Intent intent = new Intent(action);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = recordListActivity.getClass().getDeclaredField("mHeadsetReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver headReceiver = (BroadcastReceiver) mReceiverField.get(recordListActivity);
        assertNotNull(headReceiver);
        headReceiver.onReceive(recordListActivity, intent);
    }

    @Test
    public void testSDMounted() throws Exception {
        mController.create().visible();
        RecordItemFragment recordItemFragment = getRecordItemFragment();

        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = Intent.ACTION_MEDIA_MOUNTED;
        Intent intent = new Intent(action);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = recordItemFragment.getClass().getDeclaredField("mStorageReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver mStorageReceiver = (BroadcastReceiver) mReceiverField.get(recordItemFragment);
        assertNotNull(mStorageReceiver);
        mStorageReceiver.onReceive(recordItemFragment.getContext(), intent);
    }

    @Test
    public void testSDEject() throws Exception {
        mController.create().visible();
        RecordItemFragment recordItemFragment = getRecordItemFragment();

        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = Intent.ACTION_MEDIA_EJECT;
        Intent intent = new Intent(action);
        intent.setData(Uri.parse("/storage/emulated/0/recording"));

        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = recordItemFragment.getClass().getDeclaredField("mStorageReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver mStorageReceiver = (BroadcastReceiver) mReceiverField.get(recordItemFragment);
        assertNotNull(mStorageReceiver);
        mStorageReceiver.onReceive(recordItemFragment.getContext(), intent);
    }

    private static class TestOnPageChangeListener extends ViewPager.SimpleOnPageChangeListener {
        public boolean onPageSelectedCalled;

        @Override
        public void onPageSelected(int position) {
            onPageSelectedCalled = true;
        }
    }

    private RecordItemFragment getRecordItemFragment() throws Exception {
        Field mAdapterField = recordListActivity.getClass().getDeclaredField("mFragmentAdapter");
        mAdapterField.setAccessible(true);
        RecordListActivity.FragmentAdapter fragmentAdapter = (RecordListActivity.FragmentAdapter) mAdapterField.get(recordListActivity);
        Fragment fragment = fragmentAdapter.getItem(0);
        RecordItemFragment recordItemFragment = (RecordItemFragment) fragment;
        recordItemFragment.onCreateView(LayoutInflater.from(recordListActivity),
                (ViewGroup) recordListActivity.findViewById(R.id.id_page_vp), null);
        return recordItemFragment;
    }

    @Test
    public void testPermission() throws Exception {
        recordListActivity = mController.create().visible().get();
        Field beforeNeedPermissionsField = recordListActivity.getClass().getDeclaredField("mNeedRequestPermissions");
        beforeNeedPermissionsField.setAccessible(true);
        boolean beforeNeedPermissions = (boolean) beforeNeedPermissionsField.get(recordListActivity);
        assertTrue(beforeNeedPermissions);

        Shadows.shadowOf(RuntimeEnvironment.application).grantPermissions(permissions);
        mController = Robolectric.buildActivity(RecordListActivity.class);
        recordListActivity.onRequestPermissionsResult(200, permissions, new int[]{0, 0, 0});

        recordListActivity = mController.create().visible().get();
        Field afterNeedPermissionsField = recordListActivity.getClass().getDeclaredField("mNeedRequestPermissions");
        afterNeedPermissionsField.setAccessible(true);
        boolean afterNeedPermissions = (boolean) afterNeedPermissionsField.get(recordListActivity);
        assertFalse(afterNeedPermissions);
    }

    @Test
    public void testNoPermission() throws Exception {
        recordListActivity = mController.create().visible().get();
        recordListActivity.onRequestPermissionsResult(200, permissions, new int[]{-1, -1, -1});
        AlertDialog alertDialog = ShadowAlertDialog.getLatestAlertDialog();
        assertNotNull(alertDialog);
        ShadowAlertDialog shadowAlertDialog = Shadows.shadowOf(alertDialog);
        assertEquals("The app does not have critical permissions needed to run. Please check your permissions settings.", shadowAlertDialog.getMessage());
        Button okButton = alertDialog.getButton(AlertDialog.BUTTON_POSITIVE);
        assertNotNull(okButton);
        okButton.performClick();
        assertTrue(recordListActivity.isFinishing());
    }

    @Test
    public void testKeyDownBackAtPlayState() throws Exception {
        recordListActivity = mController.create().visible().get();
        RecordItemFragment recordItemFragment = getRecordItemFragment();
        RecordPreviewPlayer previewPlayer = Mockito.mock(RecordPreviewPlayer.class);
        MemberModifier.field(RecordItemFragment.class, "mPreviewPlayer")
                .set(recordItemFragment, previewPlayer);

        KeyEvent keyEvent = new KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_BACK);
        boolean flag = recordListActivity.onKeyDown(KeyEvent.KEYCODE_BACK, keyEvent);
        assertTrue(flag);

        Menu menu = Shadows.shadowOf(recordListActivity).getOptionsMenu();
        MenuItem receiveMenu = menu.findItem(R.id.setting);
        Drawable earSpeakGrayIcon = recordListActivity.getResources().getDrawable(R.drawable.ear_speak_gray);
        boolean isEarSpeakGrayIcon = earSpeakGrayIcon.getConstantState()
                .equals(receiveMenu.getIcon().getConstantState());
        assertTrue(isEarSpeakGrayIcon);
        assertFalse(recordListActivity.isFinishing());
    }

    @Test
    public void testKeyDownBackAtNoPlayState() throws Exception {
        recordListActivity = mController.create().visible().get();
        KeyEvent keyEvent = new KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_BACK);
        boolean flag = recordListActivity.onKeyDown(KeyEvent.KEYCODE_BACK, keyEvent);
        assertTrue(flag);
        assertTrue(recordListActivity.isFinishing());
    }

    @Test
    public void testToolBarAtPlayState() throws Exception {
        recordListActivity = mController.create().visible().get();
        RecordItemFragment recordItemFragment = getRecordItemFragment();
        RecordPreviewPlayer previewPlayer = Mockito.mock(RecordPreviewPlayer.class);
        MemberModifier.field(RecordItemFragment.class, "mPreviewPlayer")
                .set(recordItemFragment, previewPlayer);

        Toolbar toolbar = recordListActivity.findViewById(R.id.id_toolbar);
        assertNotNull(toolbar);
        final ArrayList<View> outViews = Lists.newArrayList();
        toolbar.findViewsWithText(outViews, toolbar.getNavigationContentDescription(), View.FIND_VIEWS_WITH_CONTENT_DESCRIPTION);
        ImageButton navigationButton = (ImageButton) outViews.get(0);
        assertNotNull(navigationButton);
        navigationButton.performClick();

        Menu menu = Shadows.shadowOf(recordListActivity).getOptionsMenu();
        MenuItem receiveMenu = menu.findItem(R.id.setting);
        Drawable earSpeakGrayIcon = recordListActivity.getResources().getDrawable(R.drawable.ear_speak_gray);
        boolean isEarSpeakGrayIcon = earSpeakGrayIcon.getConstantState()
                .equals(receiveMenu.getIcon().getConstantState());
        assertTrue(isEarSpeakGrayIcon);
        assertFalse(recordListActivity.isFinishing());
    }

    @Test
    public void testToolBarAtNoPlayState() throws Exception {
        recordListActivity = mController.create().visible().get();
        Toolbar toolbar = recordListActivity.findViewById(R.id.id_toolbar);
        assertNotNull(toolbar);
        final ArrayList<View> outViews = Lists.newArrayList();
        toolbar.findViewsWithText(outViews, toolbar.getNavigationContentDescription(), View.FIND_VIEWS_WITH_CONTENT_DESCRIPTION);
        ImageButton navigationButton = (ImageButton) outViews.get(0);
        assertNotNull(navigationButton);
        navigationButton.performClick();
        assertTrue(recordListActivity.isFinishing());
    }

}