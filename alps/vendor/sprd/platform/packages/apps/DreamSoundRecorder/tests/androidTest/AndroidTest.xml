<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<configuration description="Run APP.DreamSoundRecorder_IT_Tests.">
    <target_preparer class="com.android.tradefed.targetprep.TestAppInstallSetup">
        <option name="cleanup-apks" value="true" />
        <option name="test-file-name" value="APP.DreamSoundRecorder_IT_Tests.apk" />
        <option name="alt-dir" value="../../target/testcases/APP.DreamSoundRecorder_IT_Tests/" />
    </target_preparer>

    <option name="test-tag" value="APP.DreamSoundRecorder_IT_Tests" />

    <test class="com.android.tradefed.testtype.AndroidJUnitTest">
        <option name="package" value="com.android.soundrecorder.tests" />
        <option name="runner" value="androidx.test.runner.AndroidJUnitRunner" />
    </test>

    <result_reporter class="com.android.tradefed.result.ConsoleResultReporter" />
    <result_reporter class="com.android.tradefed.result.XmlResultReporter" />
</configuration>