/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.app.Activity;
import android.content.Context;
import android.telephony.TelephonyManager;
import com.sprd.soundrecorder.frameworks.StandardFrameworks;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(Utils.class)
public class ShadowUtils {

    @Implementation
    public static boolean checkAndBuildPermissions(Activity context) {
        return false;
    }

    @Implementation
    public static boolean isInCallState(Context context) {
        return false;
    }

}
