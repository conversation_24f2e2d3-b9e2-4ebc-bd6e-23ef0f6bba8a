/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.android.soundrecorder;

import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.runner.AndroidJUnit4;
import androidx.test.uiautomator.*;
import com.sprd.soundrecorder.MultiChooseActivity;
import com.sprd.soundrecorder.ui.RecordItemFragment;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * perform condition:
 * 1.Home activity needs: settings, email, soundrecorder,phonecall
 * 2.The status bar needs: music player、screenrecorder
 * 3.Cmcc Sim card required
 *
 * this case will crash
 */
//@RunWith(AndroidJUnit4.class)
public class MultiChoosActivityTest {
	private static final int a = 0;
    private static final int LAUNCH_TIMEOUT = 2000;
    private static final int SHORT_TIME = 500;
    private static final String mPackageName = "com.android.soundrecorder";
    private UiDevice mDevice;
    private Context context;
    private VerifyToastUtil verifyToastUtil;

    //@Before
    public void SetUp() throws Exception {
        mDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());

        try {
            if (!mDevice.isScreenOn()) {
                mDevice.wakeUp();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        //Start from home
        mDevice.pressHome();
        // Wait for launcher
        final String launcherPackage = mDevice.getLauncherPackageName();
        Assert.assertNotNull(launcherPackage);
        mDevice.wait(Until.hasObject(By.pkg(launcherPackage).depth(0)), LAUNCH_TIMEOUT);
        // launch the app
        context = ApplicationProvider.getApplicationContext();
        final Intent intent = context.getPackageManager()
                .getLaunchIntentForPackage(mPackageName);
        context.startActivity(intent);
        // Wait for the app to appear
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), SHORT_TIME);
        UiObject premissionBtn = mDevice.findObject(new UiSelector().resourceId("com.android.permissioncontroller:id/permission_allow_button"));
        try {
            premissionBtn.click();
            premissionBtn.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
        }
        verifyToastUtil = new VerifyToastUtil();
        verifyToastUtil.monitoringToastMessage();

        //start recordListActivity
        UiObject stopButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stopButton"));
        Assert.assertNotNull(stopButton);
        stopButton.click();
        //start MultiChooseActivity
        try {
            UiObject recordItem = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/middle_display"));
            recordItem.dragTo(recordItem, SHORT_TIME);
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
        }
    }

    //@Test
    public void testSelect() throws Exception {
        UiObject uiCount = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/tv_toolbar"));
        int listViewCount;
        UiObject more;
        try {
            UiObject listViewObject = mDevice.findObject(new UiSelector().resourceId("android:id/list"));
            listViewCount = listViewObject.getChildCount();
            more = mDevice.findObject(new UiSelector().description("More options"));
            more.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        if (listViewCount == 1) {
            //deselect all recording
            UiObject unAllItem = mDevice.findObject(new UiSelector().text(context.getString(R.string.menu_recording_list_deselect_all)));
            unAllItem.click();
            Assert.assertEquals(0, Integer.parseInt(uiCount.getText()));
            //select all recording
            more.click();
            UiObject allItem = mDevice.findObject(new UiSelector().text(context.getString(R.string.menu_recording_list_select_all)));
            allItem.click();
            Assert.assertEquals(uiCount.getText(), String.valueOf(listViewCount));
        } else {
            //select all recording
            UiObject allItem = mDevice.findObject(new UiSelector().text(context.getString(R.string.menu_recording_list_select_all)));
            allItem.click();
            if (listViewCount < 8) {
                Assert.assertEquals(uiCount.getText(), String.valueOf(listViewCount));
            }
            //deselect all recording
            more.click();
            UiObject unAllItem = mDevice.findObject(new UiSelector().text(context.getString(R.string.menu_recording_list_deselect_all)));
            unAllItem.click();
            Assert.assertEquals(0, Integer.parseInt(uiCount.getText()));
        }
    }

    //@Test
    public void testRename() throws Exception {
        try {
            UiObject more = mDevice.findObject(new UiSelector().description("More options"));
            more.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        //rename recording
        mDevice.findObject(new UiSelector().text(context.getString(R.string.rename))).click();

        //no modify
        UiObject okButton1 = mDevice.findObject(new UiSelector().text("SAVE"));
        okButton1.click();
        String renameMessage1 = verifyToastUtil.getToastMessage();
        Assert.assertEquals(context.getString(R.string.filename_is_not_modified), renameMessage1);

        //illegal character
        mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/inputname"))
                .setText("?");
        UiObject okButton2 = mDevice.findObject(new UiSelector().text("SAVE"));
        okButton2.click();
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        String renameMessage2 = verifyToastUtil.getToastMessage();
        Assert.assertEquals(context.getString(R.string.illegal_chars_of_filename), renameMessage2);

        //filename_empty
        mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/inputname"))
                .setText("");
        UiObject okButton3 = mDevice.findObject(new UiSelector().text("SAVE"));
        okButton3.click();
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        String renameMessage3 = verifyToastUtil.getToastMessage();
        Assert.assertEquals(context.getString(R.string.filename_empty_error), renameMessage3);

        //reanme successful
        mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/inputname"))
                .setText("IT-TEST" + System.currentTimeMillis());
        UiObject okButton4 = mDevice.findObject(new UiSelector().text("SAVE"));
        okButton4.click();
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        String renameMessage4 = verifyToastUtil.getToastMessage();
        Assert.assertEquals(context.getString(R.string.rename_save), renameMessage4);
    }

    //@Test
    public void testRenameCallRecord() throws Exception {
        try {
            //go back list
            UiObject backButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/id_toolbar")).getChild(new UiSelector().instance(0));
            backButton.click();

            //select call record list
            UiObject table = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/tabs"));
            UiObject localTable = table.getChild(new UiSelector().className("android.widget.TextView").instance(0));
            UiObject callTable = table.getChild(new UiSelector().className("android.widget.TextView").instance(1));
            Assert.assertEquals(context.getString(R.string.local_record), localTable.getText());
            Assert.assertEquals(context.getString(R.string.call_record), callTable.getText());
            Assert.assertTrue(localTable.isSelected());
            Assert.assertFalse(callTable.isSelected());
            //swipe left to call recording
            UiObject listView = mDevice.findObject(new UiSelector().resourceId("android:id/list"));
            listView.swipeLeft(50);
            Assert.assertFalse(localTable.isSelected());
            Assert.assertTrue(callTable.isSelected());
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        UiObject callListView = mDevice.findObject(new UiSelector().resourceId("android:id/list"));
        int itemCount = callListView.getChildCount();
        if (itemCount == 0) {
            return;
        }
        //start MultiChooseActivity
        UiObject recordItem = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/middle_display"));
        recordItem.dragTo(recordItem, SHORT_TIME);

        UiObject more = mDevice.findObject(new UiSelector().description("More options"));
        more.click();
        //rename recording
        mDevice.findObject(new UiSelector().text("Rename"))
                .click();
        mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/inputname"))
                .setText("SD-IT-TEST" + System.currentTimeMillis());
        UiObject okButton = mDevice.findObject(new UiSelector().text("SAVE"));
        okButton.click();
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);

        mDevice.findObject(new UiSelector().text("Android"))
                .click();
        mDevice.findObject(new UiSelector().resourceId("com.android.documentsui:id/container_save"))
                .click();
        UiObject allowButton = mDevice.findObject(new UiSelector().resourceId("com.android.documentsui:id/buttonPanel")).getChild(new UiSelector().instance(2));
        allowButton.click();
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        String renameMessage = verifyToastUtil.getToastMessage();
        Assert.assertEquals(context.getString(R.string.rename_save), renameMessage);
    }

    //@Test
    public void testDelete() throws Exception {
        //delete recording
        try {
            mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/item_delete"))
                    .click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        UiObject okButton = mDevice.findObject(new UiSelector().text("ALLOW"));
        okButton.click();
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        String deleteMessage = verifyToastUtil.getToastMessage();
        Assert.assertEquals(context.getString(R.string.recording_file_delete_success), deleteMessage);
    }

    /**
     * perform condition:has sim card
     *
     * @throws Exception
     */
    //@Test
    public void testRing() throws Exception {
        try {
            mDevice.findObject(new UiSelector().description("More options")).click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        //set ring
        mDevice.findObject(new UiSelector().text("Set as ringtone")).click();
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        String ringMessage = verifyToastUtil.getToastMessage();
        System.out.println("ringMessage:" + ringMessage);
        boolean noSimCard = ringMessage.contains(context.getString(R.string.please_insert_sim_card));
        boolean simCard0 = ringMessage.contains("set as phone ringtone");
        boolean simCard1 = ringMessage.contains("set as SIM1 Phone ringtone");
        boolean simCard2 = ringMessage.contains("set as SIM2 Phone ringtone");
        if (noSimCard) {
            Assert.assertTrue(noSimCard);
        } else if (simCard0) {
            Assert.assertTrue(simCard0);
        } else if (simCard1) {
            Assert.assertTrue(simCard1);
        } else if (simCard2) {
            Assert.assertTrue(simCard2);
        }
    }


    //@Test
    public void testDetail() throws Exception {
        String disPlayName;
        try {
            UiObject listViewObject = mDevice.findObject(new UiSelector().resourceId("android:id/list"));
            UiObject disPlayNameObject = listViewObject.getChild(new UiSelector().instance(0).resourceId(mPackageName + ":id/record_displayname"));
            disPlayName = disPlayNameObject.getText();
            UiObject more = mDevice.findObject(new UiSelector().description("More options"));
            more.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        mDevice.findObject(new UiSelector().text("View details")).click();
        UiObject fileNameValueObject = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/file_name_value"));
        String fileNameValue = fileNameValueObject.getText();
        Assert.assertEquals(disPlayName, fileNameValue);
        //close detail dialog
        UiObject cancelButton = mDevice.findObject(new UiSelector().text("CANCEL"));
        cancelButton.click();
    }

    //@Test
    public void testShare() throws Exception {
        try {
            UiObject shareButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/item_share"));
            shareButton.clickAndWaitForNewWindow();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        Thread.sleep(1000);
        String shareTitle = mDevice.findObject(new UiSelector().resourceId("android:id/title")).getText();
        Assert.assertEquals(shareTitle, context.getString(R.string.operate_share));
        //back to multi choose activity
        mDevice.pressBack();
    }

}
