/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.Manifest;
import android.app.AlertDialog;
import android.app.Dialog;
import android.bluetooth.BluetoothHeadset;
import android.bluetooth.BluetoothProfile;
import android.content.*;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.SystemClock;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.ImageButton;
import com.android.soundrecorder.R;
import com.sprd.soundrecorder.data.RecorderItem;
import com.sprd.soundrecorder.ui.RecordWaveView;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.robolectric.Robolectric;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.Shadows;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.*;

import java.lang.reflect.Field;
import java.time.Duration;

import static org.junit.Assert.*;

@RunWith(RobolectricTestRunner.class)
@Config(shadows = {ShadowSprdFrameworks.class})
public class RecordFilePlayActivityTest {

    private ActivityController<RecordFilePlayActivity> mController;
    private RecordFilePlayActivity playActivity;
    private RecorderItem item;
    @Mock
    RecordPreviewPlayer previewPlayer;
    @Mock
    RecordWaveView recordWaveView;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        Intent intent = new Intent(RuntimeEnvironment.application, RecordFilePlayActivity.class);
        item = new RecorderItem();
        item.setId(1);
        item.setData("storage/emulated/0/Android/media/com.android.soundrecorder/recordings/1.mp3");
        item.setSize(2000);
        item.setTitle("name");
        item.setDisplayName("1.mp3");
        item.setDateModify(SystemClock.currentThreadTimeMillis());
        item.setMimeType("audio/mp3");
        item.setDuration(5000);
        item.setTagNumber(2);
        intent.putExtra(RecordFilePlayActivity.PLAY_DATA, item);
        intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
        mController = Robolectric.buildActivity(RecordFilePlayActivity.class, intent);
        playActivity = mController.get();
    }

    @Test
    public void testPause() throws Exception {
        playActivity = mController.create().start().visible().get();
        ImageButton playButton = playActivity.findViewById(R.id.playButton);
        assertNotNull(playButton);
        assertTrue(playButton.hasOnClickListeners());

        MemberModifier.field(playActivity.getClass(), "mPlayer")
                .set(playActivity, previewPlayer);
        PowerMockito.when(previewPlayer.isPlaying()).thenReturn(true);

        playButton.performClick();
        assertEquals("pause", playButton.getContentDescription().toString());
    }

    @Test
    public void testResume() throws Exception {
        playActivity = mController.create().start().visible().get();
        ImageButton playButton = playActivity.findViewById(R.id.playButton);
        assertNotNull(playButton);
        assertTrue(playButton.hasOnClickListeners());

        MemberModifier.field(playActivity.getClass(), "mPlayer")
                .set(playActivity, previewPlayer);
        PowerMockito.when(previewPlayer.isPrepared()).thenReturn(true);
        MemberModifier.field(playActivity.getClass(), "mWaveView")
                .set(playActivity, recordWaveView);
        PowerMockito.when(recordWaveView.isHaveWaveData()).thenReturn(true);

        playButton.performClick();
        assertEquals("resume play", playButton.getContentDescription().toString());
    }

    @Test
    public void testNextTag() throws Exception {
        playActivity = mController.create().start().visible().get();
        Button nextTagButton = playActivity.findViewById(R.id.nextButton);
        assertNotNull(nextTagButton);
        assertTrue(nextTagButton.hasOnClickListeners());

        MemberModifier.field(playActivity.getClass(), "mPlayer")
                .set(playActivity, previewPlayer);
        PowerMockito.when(previewPlayer.isPrepared()).thenReturn(true);

        nextTagButton.performClick();
        assertFalse(nextTagButton.isEnabled());
    }

    @Test
    public void testPreviousTag() throws Exception {
        playActivity = mController.create().start().visible().get();
        Button preTagButton = playActivity.findViewById(R.id.previousButton);
        assertNotNull(preTagButton);
        assertTrue(preTagButton.hasOnClickListeners());

        MemberModifier.field(playActivity.getClass(), "mPlayer")
                .set(playActivity, previewPlayer);
        PowerMockito.when(previewPlayer.isPrepared()).thenReturn(true);

        preTagButton.performClick();
        assertFalse(preTagButton.isEnabled());
    }

    @Test
    public void testReceiveMode() throws Exception {
        playActivity = mController.create().start().visible().resume().get();
        Menu menu = Shadows.shadowOf(playActivity).getOptionsMenu();
        MenuItem receiveMenu = menu.findItem(R.id.item_volume);
        Drawable earSpeakGrayIcon = playActivity.getResources().getDrawable(R.drawable.ear_speak_gray);
        Drawable volumeSpeakIcon = playActivity.getResources().getDrawable(R.drawable.volume_speak);

        //the receive mode's icon is ear_speak without any operating
        boolean isEarSpekIcon = earSpeakGrayIcon.getConstantState()
                .equals(receiveMenu.getIcon().getConstantState());
        assertTrue(isEarSpekIcon);

        //the receive mode's icon is volume_speak when first select it
        MemberModifier.field(playActivity.getClass(), "mPlayer")
                .set(playActivity, previewPlayer);
        PowerMockito.when(previewPlayer.isPlaying()).thenReturn(true);
        ShadowSystemClock.advanceBy(Duration.ofSeconds(2));
        playActivity.onOptionsItemSelected(receiveMenu);
        boolean isVolumeSpeakIcon = volumeSpeakIcon.getConstantState()
                .equals(receiveMenu.getIcon().getConstantState());
        assertTrue(isVolumeSpeakIcon);

        //the receive mode's icon is ear_speak when second select it
        ShadowSystemClock.advanceBy(Duration.ofSeconds(4));
        playActivity.onOptionsItemSelected(receiveMenu);
        Drawable earSpeakIcon = playActivity.getResources().getDrawable(R.drawable.ear_speak);
        boolean isEarSpekIcon2 = earSpeakIcon.getConstantState()
                .equals(receiveMenu.getIcon().getConstantState());
        assertTrue(isEarSpekIcon2);

        //can not change receive mode less than one second
        playActivity.onOptionsItemSelected(receiveMenu);
        assertNotNull(ShadowToast.getLatestToast());
        assertEquals("The audio mode change is need time please wait..."
                , ShadowToast.getTextOfLatestToast());
    }

    @Test
    @Config(shadows = {ShadowAudioManager.class})
    public void testHeadOn() throws Exception {
        playActivity = mController.create().start().visible().resume().get();
        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = Intent.ACTION_HEADSET_PLUG;
        Intent intent = new Intent(action);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = playActivity.getClass().getDeclaredField("mHeadsetReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver headReceiver = (BroadcastReceiver) mReceiverField.get(playActivity);
        assertNotNull(headReceiver);
        headReceiver.onReceive(playActivity, intent);
        Menu menu = Shadows.shadowOf(playActivity).getOptionsMenu();

        MenuItem receiveMenu = menu.findItem(R.id.item_volume);
        assertFalse(receiveMenu.isVisible());
    }


    @Test
    public void testHeadOff() throws Exception {
        playActivity = mController.create().start().visible().resume().get();
        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = Intent.ACTION_HEADSET_PLUG;
        Intent intent = new Intent(action);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = playActivity.getClass().getDeclaredField("mHeadsetReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver headReceiver = (BroadcastReceiver) mReceiverField.get(playActivity);
        assertNotNull(headReceiver);
        headReceiver.onReceive(playActivity, intent);

        Menu menu = Shadows.shadowOf(playActivity).getOptionsMenu();
        MenuItem receiveMenu = menu.findItem(R.id.item_volume);
        assertTrue(receiveMenu.isVisible());
    }

    @Test
    public void testBlueHeadOn() throws Exception {
        playActivity = mController.create().start().visible().resume().get();
        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED;
        Intent intent = new Intent(action);
        intent.putExtra(BluetoothProfile.EXTRA_STATE, BluetoothProfile.STATE_CONNECTED);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = playActivity.getClass().getDeclaredField("mHeadsetReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver headReceiver = (BroadcastReceiver) mReceiverField.get(playActivity);
        assertNotNull(headReceiver);
        headReceiver.onReceive(playActivity, intent);

        Menu menu = Shadows.shadowOf(playActivity).getOptionsMenu();
        MenuItem receiveMenu = menu.findItem(R.id.item_volume);
        assertFalse(receiveMenu.isVisible());
    }

    @Test
    public void testBlueHeadOff() throws Exception {
        playActivity = mController.create().start().visible().resume().get();
        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED;
        Intent intent = new Intent(action);
        intent.putExtra(BluetoothProfile.EXTRA_STATE, BluetoothProfile.STATE_DISCONNECTED);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = playActivity.getClass().getDeclaredField("mHeadsetReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver headReceiver = (BroadcastReceiver) mReceiverField.get(playActivity);
        assertNotNull(headReceiver);
        headReceiver.onReceive(playActivity, intent);

        Menu menu = Shadows.shadowOf(playActivity).getOptionsMenu();
        MenuItem receiveMenu = menu.findItem(R.id.item_volume);
        assertTrue(receiveMenu.isVisible());
    }

    @Test
    public void testSDEject() throws Exception {
        playActivity = mController.create().start().visible().resume().get();
        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        String action = Intent.ACTION_MEDIA_EJECT;
        Intent intent = new Intent(action);
        intent.setData(Uri.parse("storage/emulated/0/recordings/1.mp3"));
        assertTrue(shadowApplication.hasReceiverForIntent(intent));

        Field mReceiverField = playActivity.getClass().getDeclaredField("mStorageReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver mStorageReceiver = (BroadcastReceiver) mReceiverField.get(playActivity);
        assertNotNull(mStorageReceiver);
        mStorageReceiver.onReceive(playActivity, intent);

        assertTrue(playActivity.isFinishing());
    }

    @Test
    public void testQuiteActivity() throws Exception {
        playActivity = mController.create().start().visible().resume().get();
        RecordPreviewPlayer mPlayer = new RecordPreviewPlayer(RuntimeEnvironment.application);
        MemberModifier.field(playActivity.getClass(), "mPlayer").set(playActivity, mPlayer);
        mController.pause();
        assertNotNull(ShadowToast.getLatestToast());
        assertEquals(playActivity.getString(R.string.background_speak_close), ShadowToast.getTextOfLatestToast());

        Dialog mAlertDialog = new AlertDialog.Builder(RuntimeEnvironment.application).show();
        MemberModifier.field(playActivity.getClass(), "mAlertDialog").set(playActivity, mAlertDialog);
        AlertDialog beforeDialog = ShadowAlertDialog.getLatestAlertDialog();
        ShadowAlertDialog shadowBeforeDialog = Shadows.shadowOf(beforeDialog);
        assertFalse(shadowBeforeDialog.hasBeenDismissed());
        mController.stop();
        AlertDialog afterDialog = ShadowAlertDialog.getLatestAlertDialog();
        ShadowAlertDialog shadowAfterDialog = Shadows.shadowOf(afterDialog);
        assertTrue(shadowAfterDialog.hasBeenDismissed());

        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        Intent intent = new Intent(Intent.ACTION_MEDIA_EJECT);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));
        mController.destroy();
        assertFalse(shadowApplication.hasReceiverForIntent(intent));
    }

    @Test
    public void testRequestPermissionsResult() {
        playActivity = mController.create().get();
        String[] permissions = {Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_PHONE_STATE};
        int[] grantResults = new int[]{0, 0, 1};
        playActivity.onRequestPermissionsResult(200, permissions, grantResults);
        AlertDialog brforeDialog = ShadowAlertDialog.getLatestAlertDialog();
        assertNotNull(brforeDialog);
        assertEquals(playActivity.getString(R.string.error_permissions),Shadows.shadowOf(brforeDialog).getMessage());
        grantResults = new int[]{0, 0, 0};
        playActivity.onRequestPermissionsResult(200, permissions, grantResults);
    }

}