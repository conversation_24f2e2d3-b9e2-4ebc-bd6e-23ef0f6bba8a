/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Intent;
import android.content.IntentSender;
import android.net.Uri;
import android.os.SystemClock;
import android.provider.MediaStore;
import android.util.SparseArray;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Lifecycle;
import com.android.soundrecorder.R;
import com.sprd.soundrecorder.data.DataOpration;
import com.sprd.soundrecorder.data.RecorderItem;
import com.sprd.soundrecorder.ui.RecordItemFragment;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.robolectric.Robolectric;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.Shadows;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.fakes.RoboCursor;
import org.robolectric.fakes.RoboMenu;
import org.robolectric.fakes.RoboMenuItem;
import org.robolectric.shadows.*;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.Assert.*;
import static org.robolectric.Shadows.shadowOf;

@RunWith(RobolectricTestRunner.class)
@Config(shadows = {ShadowSprdFrameworks.class})
public class MultiChooseActivityTest {

    private ActivityController<MultiChooseActivity> mController;
    private MultiChooseActivity multiChooseActivity;
    Object[][] datas = new Object[][]{
            {1, "/storage/emulated/0/Android/media/com.android.soundrecorder/recordings/0.mp3", 2000, "name0", "0.mp3", SystemClock.currentThreadTimeMillis(), "audio/mp3", 5000, 0},
            {2, "/storage/emulated/0/Android/media/com.android.soundrecorder/recordings/1.mp3", 2000, "name1", "1.mp3", SystemClock.currentThreadTimeMillis(), "audio/mp3", 5000, 0},
            {3, "/storage/emulated/0/Android/media/com.android.soundrecorder/recordings/2.mp3", 2000, "name2", "2.mp3", SystemClock.currentThreadTimeMillis(), "audio/mp3", 5000, 0},
            {4, "/storage/emulated/0/Android/media/com.android.soundrecorder/recordings/3.mp3", 2000, "name3", "3.mp3", SystemClock.currentThreadTimeMillis(), "audio/mp3", 5000, 0},
            {5, "/storage/emulated/0/Android/media/com.android.soundrecorder/recordings/4.mp3", 2000, "name4", "4.mp3", SystemClock.currentThreadTimeMillis(), "audio/mp3", 5000, 0}
    };

    @Before
    public void setUp() throws Exception {
        MemberModifier.field(RecordItemFragment.class, "Ismulclick").set(new RecordItemFragment(), true);
        mController = Robolectric.buildActivity(MultiChooseActivity.class);
        multiChooseActivity = mController.get();
    }

    @Test
    public void testActivityLifecycle() {
        mController.get().setTheme(R.style.AppTheme);
        mController.create();
        Lifecycle lifecycle = multiChooseActivity.getLifecycle();
        assertEquals("CREATED", lifecycle.getCurrentState().name());
        mController.start();
        assertEquals("STARTED", lifecycle.getCurrentState().name());
        mController.resume();
        assertEquals("RESUMED", lifecycle.getCurrentState().name());
        mController.stop();
        mController.destroy();
        assertEquals("DESTROYED", lifecycle.getCurrentState().name());
    }

    @Test
    public void testSelectAll() throws Exception {
        setSoundData(datas);
        mController.create().start().visible().resume();
        multiChooseActivity = mController.get();
        MenuItem selectMenu = new RoboMenuItem(R.id.sub_item_select_all);

        Field toolbarListenerField = multiChooseActivity.getClass().getDeclaredField("mToolBarMenuItemClickListener");
        toolbarListenerField.setAccessible(true);
        Toolbar.OnMenuItemClickListener toolbarListener = (Toolbar.OnMenuItemClickListener) toolbarListenerField.get(multiChooseActivity);
        assertNotNull(toolbarListener);
        toolbarListener.onMenuItemClick(selectMenu);

        Toolbar toolbar = multiChooseActivity.findViewById(R.id.id_toolbar);
        Menu menu = toolbar.getMenu();
        MenuItem deleteMenu = menu.findItem(R.id.item_delete);
        assertTrue(deleteMenu.isVisible());
    }

    @Test
    public void testNoSelectAll() throws Exception {
        testSelectAll();
        Menu roboMenu = new RoboMenu(RuntimeEnvironment.application);
        multiChooseActivity.onPrepareOptionsMenu(roboMenu);
        MenuItem selectMenu = new RoboMenuItem(R.id.sub_item_select_all);

        Field toolbarListenerField = multiChooseActivity.getClass().getDeclaredField("mToolBarMenuItemClickListener");
        toolbarListenerField.setAccessible(true);
        Toolbar.OnMenuItemClickListener toolbarListener = (Toolbar.OnMenuItemClickListener) toolbarListenerField.get(multiChooseActivity);
        assertNotNull(toolbarListener);
        toolbarListener.onMenuItemClick(selectMenu);

        Toolbar toolbar = multiChooseActivity.findViewById(R.id.id_toolbar);
        Menu menu = toolbar.getMenu();
        MenuItem deleteMenu = menu.findItem(R.id.item_delete);
        assertFalse(deleteMenu.isVisible());
    }

    /**
     * perform condition: must have 0.mp3
     *
     * @throws Exception
     */
    @Test
    @Config(shadows = {ShadowDataOpration.class})
    public void testRename() throws Exception {
        mController.create().start().visible().resume();
        multiChooseActivity = mController.get();
        MenuItem renameMenu = new RoboMenuItem(R.id.sub_item_rename);

        Field toolbarListenerField = multiChooseActivity.getClass().getDeclaredField("mToolBarMenuItemClickListener");
        toolbarListenerField.setAccessible(true);
        Toolbar.OnMenuItemClickListener toolbarListener = (Toolbar.OnMenuItemClickListener) toolbarListenerField.get(multiChooseActivity);
        assertNotNull(toolbarListener);
        toolbarListener.onMenuItemClick(renameMenu);

        Dialog dialog = ShadowDialog.getLatestDialog();
        assertNotNull(dialog);
        EditText newName = dialog.findViewById(R.id.inputname);
        newName.setText("newName");
        Button okButton = dialog.findViewById(R.id.button_ok);
        okButton.performClick();

        assertNotNull(ShadowToast.getLatestToast());
        System.out.println("toast:" + ShadowToast.getTextOfLatestToast());
        assertEquals("Rename successfully", ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void testDetail() throws Exception {
        setSoundData(datas);
        mController.create().start().visible().resume();
        multiChooseActivity = mController.get();
        MenuItem deailMenu = new RoboMenuItem(R.id.sub_view_details);

        Field toolbarListenerField = multiChooseActivity.getClass().getDeclaredField("mToolBarMenuItemClickListener");
        toolbarListenerField.setAccessible(true);
        Toolbar.OnMenuItemClickListener toolbarListener = (Toolbar.OnMenuItemClickListener) toolbarListenerField.get(multiChooseActivity);
        assertNotNull(toolbarListener);
        toolbarListener.onMenuItemClick(deailMenu);

        Dialog dialog = ShadowDialog.getLatestDialog();
        assertNotNull(dialog);
        TextView fiePathTV = dialog.findViewById(R.id.file_path_value);
        assertNotNull(fiePathTV);
        assertEquals("/storage/emulated/0/Android/media/com.android.soundrecorder/recordings", fiePathTV.getText().toString());
    }

    @Test
    public void testShare() throws Exception {
        setSoundData(datas);
        mController.create().start().visible().resume();
        multiChooseActivity = mController.get();
        MenuItem shareMenu = new RoboMenuItem(R.id.item_share);

        Field toolbarListenerField = multiChooseActivity.getClass().getDeclaredField("mToolBarMenuItemClickListener");
        toolbarListenerField.setAccessible(true);
        Toolbar.OnMenuItemClickListener toolbarListener = (Toolbar.OnMenuItemClickListener) toolbarListenerField.get(multiChooseActivity);
        assertNotNull(toolbarListener);
        toolbarListener.onMenuItemClick(shareMenu);

        Intent exceptIntent = new Intent(Intent.ACTION_SEND);
        Intent actual = Shadows.shadowOf(RuntimeEnvironment.application)
                .getNextStartedActivity();
        assertNotNull(actual);
        assertEquals(exceptIntent.getComponent(), actual.getComponent());
    }

    /**
     * perform condition: must have 1.mp3
     *
     * @throws Exception
     */
//    @Test
    public void testDelete() throws Exception {
        setSoundData(datas);
        mController.create().start().visible().resume();
        multiChooseActivity = mController.get();

        ListView listView = multiChooseActivity.findViewById(android.R.id.list);
        View view0 = listView.getAdapter().getView(0, null, null);
        listView.performItemClick(view0, 0, 0);
        View view1 = listView.getAdapter().getView(1, null, null);
        listView.performItemClick(view1, 1, 0);

        MenuItem deleteMenu = new RoboMenuItem(R.id.item_delete);

        Field toolbarListenerField = multiChooseActivity.getClass().getDeclaredField("mToolBarMenuItemClickListener");
        toolbarListenerField.setAccessible(true);
        Toolbar.OnMenuItemClickListener toolbarListener = (Toolbar.OnMenuItemClickListener) toolbarListenerField.get(multiChooseActivity);
        assertNotNull(toolbarListener);

        toolbarListener.onMenuItemClick(deleteMenu);

        AlertDialog alertDialog = ShadowAlertDialog.getLatestAlertDialog();
        assertNotNull(alertDialog);
        ShadowAlertDialog shadowAlertDialog = Shadows.shadowOf(alertDialog);
        assertEquals("Are you sure to delete the file?", shadowAlertDialog.getMessage().toString());
        Button button = alertDialog.getButton(AlertDialog.BUTTON_POSITIVE);
        assertNotNull(button);
        button.performClick();

        assertNotNull(ShadowToast.getLatestToast());
        assertEquals("Recording deleted", ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void testRingtone() throws Exception {
        setSoundData(datas);
        mController.create().start().visible().resume();
        multiChooseActivity = mController.get();
        MenuItem ringtoneMenu = new RoboMenuItem(R.id.sub_item_set_ring);

        Field toolbarListenerField = multiChooseActivity.getClass().getDeclaredField("mToolBarMenuItemClickListener");
        toolbarListenerField.setAccessible(true);
        Toolbar.OnMenuItemClickListener toolbarListener = (Toolbar.OnMenuItemClickListener) toolbarListenerField.get(multiChooseActivity);
        assertNotNull(toolbarListener);
        toolbarListener.onMenuItemClick(ringtoneMenu);

        assertNotNull(ShadowToast.getLatestToast());
        assertEquals("Please insert a SIM card at least", ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void testDeleteResult() {
        setSoundData(datas);
        mController.create().start().visible().resume();
        //delete successful
        multiChooseActivity.onActivityResult(1, Activity.RESULT_OK, null);
        assertNotNull(ShadowToast.getLatestToast());
        assertEquals("Recording(s) deleted", ShadowToast.getTextOfLatestToast());
        //cancel delete dialog
        multiChooseActivity.onActivityResult(1, Activity.RESULT_CANCELED, null);
        multiChooseActivity.onActivityResult(1, Activity.RESULT_FIRST_USER, null);
        assertNotNull(ShadowToast.getLatestToast());
        assertEquals("Failed to delete recording file", ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void testRenameResult() throws Exception {
        //have not permission
        Intent intent = new Intent();
        intent.setData(Uri.parse("content://com.android.externalstorage.documents/tree/primary%3AAndroid"));
        SparseArray<RecorderItem> mCheckedItems = new SparseArray<>();
        RecorderItem recorderItem = new RecorderItem();
        recorderItem.setData("/storage/emulated/0/Android/media/com.android.soundrecorder/recordings/0.mp3");
        recorderItem.setDateModify(System.currentTimeMillis());
        recorderItem.setDuration(5000);
        recorderItem.setDisplayName("0.mp3");
        mCheckedItems.put(0, recorderItem);
        MemberModifier.field(multiChooseActivity.getClass(), "mNewCallFileName").set(multiChooseActivity, "newName");
        MemberModifier.field(multiChooseActivity.getClass(), "mCheckedItems").set(multiChooseActivity, mCheckedItems);
        //rename successful
        multiChooseActivity.onActivityResult(2, Activity.RESULT_OK, intent);
        assertNotNull(ShadowToast.getLatestToast());
        assertEquals("Rename successfully", ShadowToast.getTextOfLatestToast());
        //rename cancel
        multiChooseActivity.onActivityResult(2, Activity.RESULT_CANCELED, intent);
        AlertDialog alertDialog2 = ShadowAlertDialog.getLatestAlertDialog();
        assertNotNull(alertDialog2);
        ShadowAlertDialog shadowAlertDialog2 = Shadows.shadowOf(alertDialog2);
        assertEquals(multiChooseActivity.getString(R.string.document_request_confirm), shadowAlertDialog2.getMessage().toString());
    }

    @Test
    public void testStorageReceiver() throws Exception {
        mController.create().start().visible().resume();
        ShadowApplication shadowApplication = ShadowApplication.getInstance();
        Intent intent = new Intent(Intent.ACTION_MEDIA_EJECT);
        assertTrue(shadowApplication.hasReceiverForIntent(intent));
        Field mReceiverField = multiChooseActivity.getClass().getDeclaredField("mStorageReceiver");
        mReceiverField.setAccessible(true);
        BroadcastReceiver mReceiver = (BroadcastReceiver) mReceiverField.get(multiChooseActivity);
        assertNotNull(mReceiver);
        MemberModifier.field(multiChooseActivity.getClass(), "hasExternalFile").set(multiChooseActivity, true);
        Dialog mAlertDialog = new AlertDialog.Builder(RuntimeEnvironment.application).show();
        MemberModifier.field(multiChooseActivity.getClass(), "mAlertDialog").set(multiChooseActivity, mAlertDialog);
        AlertDialog beforeDialog = ShadowAlertDialog.getLatestAlertDialog();
        ShadowAlertDialog shadowBeforeDialog = Shadows.shadowOf(beforeDialog);
        assertFalse(shadowBeforeDialog.hasBeenDismissed());

        mReceiver.onReceive(multiChooseActivity.getApplicationContext(), intent);

        AlertDialog afterDialog = ShadowAlertDialog.getLatestAlertDialog();
        ShadowAlertDialog shadowAfterDialog = Shadows.shadowOf(afterDialog);
        assertTrue(shadowAfterDialog.hasBeenDismissed());
    }

    private void setSoundData(Object[][] datas) {

        RoboCursor cursor = new RoboCursor() {
            @Override
            public boolean isClosed() {
                return true;
            }
        };
        cursor.setColumnNames(Arrays.asList(
                MediaStore.Audio.Media._ID,
                MediaStore.Audio.Media.DATA,
                MediaStore.Audio.Media.SIZE,
                MediaStore.Audio.Media.TITLE,
                MediaStore.Audio.Media.DISPLAY_NAME,
                MediaStore.Audio.Media.DATE_MODIFIED,
                MediaStore.Audio.Media.MIME_TYPE,
                MediaStore.Audio.Media.DURATION,
                MediaStore.Audio.Media.BOOKMARK));
        cursor.setResults(datas);
        shadowOf(RuntimeEnvironment.application.getContentResolver())
                .setCursor(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, cursor);
    }

}