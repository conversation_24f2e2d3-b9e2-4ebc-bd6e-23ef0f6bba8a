/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.media.AudioManager;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(AudioManager.class)
public class ShadowAudioManager {
    @Implementation
    public boolean isWiredHeadsetOn() {
        return true;
    }
}
