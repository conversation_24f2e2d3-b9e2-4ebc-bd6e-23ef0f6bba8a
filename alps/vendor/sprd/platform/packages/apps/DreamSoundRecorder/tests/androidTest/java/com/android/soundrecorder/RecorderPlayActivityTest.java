/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.android.soundrecorder;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.runner.AndroidJUnit4;
import androidx.test.uiautomator.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * perform condition:
 * 1.Home activity needs: settings, email, soundrecorder,phonecall
 * 2.The status bar needs: music player、screenrecorder
 * 3.Cmcc Sim card required
 */
//@RunWith(AndroidJUnit4.class)
public class RecorderPlayActivityTest {
    private static final int a = 0;
    private static final int LAUNCH_TIMEOUT = 500;
    private static final String mPackageName = "com.android.soundrecorder";
    private UiDevice mDevice;
    private Context context;
    private VerifyToastUtil verifyToastUtil;

    //@Before
    public void SetUp() throws Exception {
        mDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());

        try {
            if (!mDevice.isScreenOn()) {
                mDevice.wakeUp();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        //Start from home
        mDevice.pressHome();
        // Wait for launcher
        final String launcherPackage = mDevice.getLauncherPackageName();
        Assert.assertNotNull(launcherPackage);
        // launch the app
        context = ApplicationProvider.getApplicationContext();
        final Intent intent = context.getPackageManager()
                .getLaunchIntentForPackage(mPackageName);
        context.startActivity(intent);
        // Wait for the app to appear
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), LAUNCH_TIMEOUT);
        UiObject premissionBtn = mDevice.findObject(new UiSelector().resourceId("com.android.permissioncontroller:id/permission_allow_button"));
        try {
            premissionBtn.click();
            premissionBtn.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
        }

        //start recordListActivity
        UiObject stopButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/stopButton"));
        Assert.assertNotNull(stopButton);
        stopButton.clickAndWaitForNewWindow();
        //start RecordFilePlayActivity
        try {
            UiObject recordItem = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/record_item"));
            recordItem.clickAndWaitForNewWindow();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
        }
        verifyToastUtil = new VerifyToastUtil();
        verifyToastUtil.monitoringToastMessage();
    }

    //@Test
    public void testRecordFilePlay() throws Exception {
        UiObject playButton;
        try {
            //play recording
            playButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/playButton"));
            Assert.assertEquals(context.getString(R.string.start_play), playButton.getContentDescription());
            playButton.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        Assert.assertEquals(context.getString(R.string.pause), playButton.getContentDescription());
        //receiver mode on
        UiObject itemVolume = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/item_volume"));
        itemVolume.click();
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), 50);
        String modeOnMessage = verifyToastUtil.getToastMessage();
        Assert.assertEquals(context.getString(R.string.speak_open), modeOnMessage);
        //pause recording
        playButton.click();
        //receiver mode off
        itemVolume.click();
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), 50);
        String modeOffMessage = verifyToastUtil.getToastMessage();
        Assert.assertEquals(context.getString(R.string.speak_close), modeOffMessage);
    }

    //@Test
    public void testSeekTag() throws Exception {
        //play recording
        UiObject playButton;
        try {
            playButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/playButton"));
            Assert.assertEquals(context.getString(R.string.start_play), playButton.getContentDescription());
            playButton.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        Assert.assertEquals(context.getString(R.string.pause), playButton.getContentDescription());

        UiObject nextTagButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/nextButton"));
        UiObject preTagButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/previousButton"));
        while (nextTagButton.isEnabled() || preTagButton.isEnabled()) {
            if (nextTagButton.isEnabled()) {
                nextTagButton.click();
                break;
            } else if (preTagButton.isEnabled()) {
                preTagButton.click();
                break;
            }
        }
    }

    /**
     * perform condition: navigation bar have music
     *
     * @throws Exception
     */
    //@Test
    public void testLoseAudioAtPlaying() throws Exception {
        UiObject playButton;
        try {
            playButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/playButton"));
            Assert.assertEquals(context.getString(R.string.start_play), playButton.getContentDescription());
            playButton.click();
            Assert.assertEquals(context.getString(R.string.pause), playButton.getContentDescription());
            //play music
            mDevice.swipe(300, 0, 300, 800, 50);
            mDevice.waitForIdle(1000);

            UiObject musicButton = mDevice.findObject(new UiSelector().resourceId("com.android.music:id/toggle_btn"));
            musicButton.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        mDevice.waitForIdle(1000);
        mDevice.swipe(300, 800, 300, 0, 50);
        mDevice.waitForIdle(1000);
        mDevice.swipe(300, 800, 300, 0, 50);
        mDevice.waitForIdle(1000);
        mDevice.swipe(300, 800, 300, 0, 50);
        Assert.assertEquals(context.getString(R.string.resume_play), playButton.getContentDescription());
    }

    //@Test
    public void testMoveWave() throws Exception {
        UiObject playButton;
        try {
            //play recording
            playButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/playButton"));
            Assert.assertEquals(context.getString(R.string.start_play), playButton.getContentDescription());
            playButton.click();
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        Assert.assertEquals(context.getString(R.string.pause), playButton.getContentDescription());
        //pause recording
        playButton.click();
        String beforeTime = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/timerView")).getText();
        UiObject waveView = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/wavesLayout"));
        waveView.swipeRight(100);
        String afterTime = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/timerView")).getText();
        assertTrue(!beforeTime.equals(afterTime));
    }

    //@Test
    public void testAudioNoisyBroadcast() throws Exception {
        try {
            //play recording
            UiObject playButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/playButton"));
            Assert.assertEquals(context.getString(R.string.start_play), playButton.getContentDescription());
            playButton.click();
            Assert.assertEquals(context.getString(R.string.pause), playButton.getContentDescription());
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        //send audio noisy broadcast
        mDevice.executeShellCommand("am broadcast -a android.media.AUDIO_BECOMING_NOISY");
    }

    //@Test
    public void testHeadsetPlugBroadcast() throws Exception {
        try {
            //play recording
            UiObject playButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/playButton"));
            Assert.assertEquals(context.getString(R.string.start_play), playButton.getContentDescription());
            playButton.click();
            Assert.assertEquals(context.getString(R.string.pause), playButton.getContentDescription());
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        mDevice.executeShellCommand("am broadcast -a android.intent.action.HEADSET_PLUG");
        UiObject itemVolume = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/item_volume"));
        Assert.assertNotNull(itemVolume);
    }

    //@Test
    public void testBlueHeadsetDisConnectedBroadcast() throws Exception {
        mDevice.executeShellCommand("am broadcast -a android.bluetooth.headset.profile.action.CONNECTION_STATE_CHANGED --ei android.bluetooth.profile.extra.STATE " + BluetoothProfile.STATE_DISCONNECTED);
        UiObject itemVolume = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/item_volume"));
        Assert.assertNotNull(itemVolume);
    }

    //@Test
    public void testBlueHeadsetConnectedBroadcast() throws Exception {
        try {
            //play recording
            UiObject playButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/playButton"));
            Assert.assertEquals(context.getString(R.string.start_play), playButton.getContentDescription());
            playButton.click();
            Assert.assertEquals(context.getString(R.string.pause), playButton.getContentDescription());
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        mDevice.executeShellCommand("am broadcast -a android.bluetooth.headset.profile.action.CONNECTION_STATE_CHANGED --ei android.bluetooth.profile.extra.STATE " + BluetoothProfile.STATE_CONNECTED);
        mDevice.waitForIdle(2000);
    }

    //    @Test
    public void testBlueHeadsetChangedBroadcast() throws Exception {
        mDevice.executeShellCommand("am broadcast -a android.bluetooth.adapter.action.STATE_CHANGED --ei android.bluetooth.profile.extra.STATE " + BluetoothAdapter.STATE_OFF);
        UiObject itemVolume = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/item_volume"));
        Assert.assertNotNull(itemVolume);
    }

    //Test
    public void testHeadsetPauseBroadcast() throws Exception {
        UiObject playButton;
        try {
            playButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/playButton"));
            Assert.assertEquals(context.getString(R.string.start_play), playButton.getContentDescription());
        } catch (UiObjectNotFoundException e) {
            e.printStackTrace();
            return;
        }
        mDevice.executeShellCommand("am broadcast -a com.android.soundrecordermain.HEADSET.PAUSE");
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), 500);
        Assert.assertEquals(context.getString(R.string.pause), playButton.getContentDescription());
        mDevice.executeShellCommand("am broadcast -a com.android.soundrecordermain.HEADSET.PAUSE");
        Assert.assertEquals(context.getString(R.string.resume_play), playButton.getContentDescription());
        mDevice.waitForIdle(2000);
    }

    //@Test
    public void testShutDownBroadcast() throws Exception {
        //play recording
        try {
            UiObject playButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/playButton"));
            Assert.assertEquals(context.getString(R.string.start_play), playButton.getContentDescription());
            playButton.click();
            Assert.assertEquals(context.getString(R.string.pause), playButton.getContentDescription());
        }catch (UiObjectNotFoundException e){
            e.printStackTrace();
            return;
        }

        mDevice.executeShellCommand("am broadcast -a android.intent.action.ACTION_SHUTDOWN");
        mDevice.wait(Until.hasObject(By.pkg(mPackageName).depth(0)), 200);
        mDevice.waitForIdle(2000);
    }

    //@Test
    public void testStatusPhoneBroadcast() throws Exception {
        try {
            //play recording
            UiObject playButton = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/playButton"));
            Assert.assertEquals(context.getString(R.string.start_play), playButton.getContentDescription());
            playButton.click();
            Assert.assertEquals(context.getString(R.string.pause), playButton.getContentDescription());
        }catch (UiObjectNotFoundException e){
            e.printStackTrace();
            return;
        }

        mDevice.executeShellCommand("am broadcast -a android.intent.action.PHONE_STATE");
        UiObject itemVolume = mDevice.findObject(new UiSelector().resourceId(mPackageName + ":id/item_volume"));
        Assert.assertNotNull(itemVolume);
        Thread.sleep(2000);
    }

}
