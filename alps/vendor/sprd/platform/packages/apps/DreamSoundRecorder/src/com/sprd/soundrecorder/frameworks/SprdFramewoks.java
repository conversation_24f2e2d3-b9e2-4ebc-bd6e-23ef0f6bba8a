/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder.frameworks;

import android.content.Context;
import android.media.AudioSystem;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.SystemProperties;
import android.os.unisocpower.PowerManagerEx;
import android.provider.MediaStore;
import android.telephony.TelephonyManager;
import android.util.UniFrameworkComponentFactory;

import com.sprd.soundrecorder.frameworks.EnvironmentEx;

import java.io.File;
import java.util.List;

/**
 * Created by jian.xu on 2017/4/1.
 */

public class SprdFramewoks extends StandardFrameworks {

    private static final String TAG = "SprdFramewoks";
    private static final String PACKAGE_NAME = "com.android.soundrecorder";

    //audio
    @Override
    public boolean isAudioSourceActive(int source) {
        return AudioSystem.isSourceActive(source);
    }

    //mediastore
    @Override
    public String getMediaStoreAlbumArtist() {
        return MediaStore.Audio.Media.ALBUM_ARTIST;
    }

    @Override
    public int getDefaultStorageLocation(Context context) {
        return -1;
    }

    @Override
    public String getInternalStoragePathState() {
        return EnvironmentEx.getInternalStoragePathState();
    }

    @Override
    public File getInternalStoragePath() {
        return EnvironmentEx.getInternalStoragePath();
    }

    @Override
    public String getExternalStoragePathState() {
        return EnvironmentEx.getExternalStoragePathState();
    }

    @Override
    public File getExternalStoragePath() {
        return EnvironmentEx.getExternalStoragePath();
    }

    //system
    @Override
    public String getSystemProperties(String name, String default_value) {
        return SystemProperties.get(name, default_value);
    }

    @Override
    public boolean getBooleanFromSystemProperties(String name, boolean default_value) {
        return SystemProperties.getBoolean(name, default_value);
    }

    //tele
    @Override
    public boolean getTeleHasIccCard(TelephonyManager tm, int slotId) {
        return tm.hasIccCard(slotId);
    }

    @Override
    public void setActualDefaultRingtoneUri(Context context, int type, Uri ringtoneUri, int phoneId) {
        //bug1236667 :not set audio as ringtone correctly
//        int ringtoneType = (phoneId == 1) ? RingtoneManager.TYPE_RINGTONE1 : RingtoneManager.TYPE_RINGTONE;
        int ringtoneType = (phoneId == 1) ? 8 : RingtoneManager.TYPE_RINGTONE;
        RingtoneManager.setActualDefaultRingtoneUri(context, ringtoneType, ringtoneUri);
    }

    /*@Override
    public File[] getUsbStoragePath() {//bug 663417 when connect the otg u volume the first entry filelist is empty
        return EnvironmentEx.getUsbdiskVolumePaths();
    } */

    public boolean isUltraPowerSaveMode(Context context) {
        try {
            PowerManagerEx mPowerManagerEx
                    = UniFrameworkComponentFactory.getInstance().getPowerManagerEx();
            return mPowerManagerEx.isUltraPowerSaveMode();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public boolean isAllowedAppListInUltraSavingMode(Context context) {
        boolean result = false;
        try {
            PowerManagerEx mPowerManagerEx
                    = UniFrameworkComponentFactory.getInstance().getPowerManagerEx();
            List<String> allowedAppList = mPowerManagerEx.getAllowedAppListInUltraSavingMode();

            for (String app : allowedAppList) {
                android.util.Log.d(TAG, "allowedApp:" + app);
                if (app.contains(PACKAGE_NAME)) {
                    result = true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public String getPowerSaveChangeAction() {
        return PowerManagerEx.ACTION_POWEREX_SAVE_MODE_CHANGED;
    }

    @Override
    public String getPowerSaveMode() {
        return PowerManagerEx.EXTRA_POWEREX_SAVE_MODE;
    }
}
