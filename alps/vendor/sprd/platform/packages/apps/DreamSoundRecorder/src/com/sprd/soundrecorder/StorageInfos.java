/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.content.Context;
import android.os.Environment;
import android.os.StatFs;
import android.util.Log;

import com.sprd.soundrecorder.frameworks.EnvironmentEx;
import com.sprd.soundrecorder.frameworks.StandardFrameworks;
import com.sprd.soundrecorder.service.SprdRecorder;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/* SPRD: fix bug 250201 @{ */

/**
 * Storage access to information related to the class of functions
 * Main fucntion: according to the corresponding access storage shceme for different information
 *
 * <AUTHOR>
 */
public class StorageInfos {
    private static final String TAG = "StorageInfos";
    public final static String RECORD_RELATIVE_PATH = "/Android/media/com.android.soundrecorder";
    public final static String CALL_RELATIVE_PATH = "/Music/voicecall";
    public static final boolean USE_MUSIC = true;
    public static final String MUSIC_DIR = "/Music";
    /**
     * judge the internal storage state
     *
     * @return is mounted or not
     */
    public static boolean isInternalStorageMounted() {
        String state = StandardFrameworks.getInstances().getInternalStoragePathState();
        return Environment.MEDIA_MOUNTED.equals(state);
    }

    /**
     * get the internal storage directory
     *
     * @return internal storage directory
     */
    public static File getInternalStorageDirectory() {
        return StandardFrameworks.getInstances().getInternalStoragePath();
    }

    /**
     * judge the External storage state
     *
     * @return is mounted or not
     */
    public static boolean isExternalStorageMounted() {
        String state = StandardFrameworks.getInstances().getExternalStoragePathState();
        return Environment.MEDIA_MOUNTED.equals(state);
    }

    /**
     * get the External storage directory
     *
     * @return External storage directory
     */
    public static File getExternalStorageDirectory() {
        return StandardFrameworks.getInstances().getExternalStoragePath();
    }

    /**
     * Get the external storage path wherever sdcard is exist, or not.
     *
     * @return external storage path
     */
    public static String getExternalStorageDir() {
        //modify for DAHLIA-5154 resolve nulloption exception by laijie 20250603 begin
        File storageDirectory = getExternalStorageDirectory();
        if (storageDirectory == null){
            Log.w(TAG, "getExternalStorageDir,getExternalStorageDirectory is null");
            return "error";
        }
        return storageDirectory.getAbsolutePath();
        //modify for DAHLIA-5154 resolve nulloption exception by laijie 20250603 end
    }

    /**
     * judge whether the scheme for NAND
     *
     * @return true or false
     */
    public static boolean isInternalStorageSupported() {
        boolean support = false;
        if ("1".equals(StandardFrameworks.getInstances().
                getSystemProperties("ro.device.support.nand", "0"))) {
            support = true;
        }
        return support;
    }
    /* @} */

    /**
     * judge whether the path is in the external storage, or not
     *
     * @param path
     * @return
     */
    public static boolean isInExternalSDCard(String path) {
        if (path == null) {
            return false;
        }

        return StorageInfos.isExternalStorageMounted() && path.startsWith(StorageInfos.getExternalStorageDir());
    }

    public static boolean isPathExistAndCanWrite(String path) {
        if (path == null) {
            return false;
        }
        File filePath = new File(path);
        return filePath.exists() && filePath.canWrite();
    }

    /**
     * check the storage space is enough
     * internal available space < 5%
     * external availabel space < 50K return false
     *
     * @return true is enough,or false
     */
    public static boolean haveEnoughStorage(String path) {
        boolean isEnough = true;
        boolean isExternalUsed = StorageInfos.isInExternalSDCard(path);

        File savePath = null;
        if (isExternalUsed) {
            savePath = StorageInfos.getExternalStorageDirectory();
        } else {
            savePath = StorageInfos.getInternalStorageDirectory();
        }
        if (isInUsbCard(path)) {//bug 667315 the otg storage is full the pop is error
            savePath = getUSBStorage();
            Log.d("haveEnoughStorage", "sprd savepath=" + savePath);
        }

        /** SPRD:Bug 617209 T-card set out into the internal storage, abnormal sound recorder settings  ( @{ */
        try {
            if (savePath != null) {
                final StatFs stat = new StatFs(savePath.getPath());
                final long blockSize = stat.getBlockSize();
                final long availableBlocks = stat.getAvailableBlocks();
                long mAvailSize = availableBlocks * blockSize;
                isEnough = mAvailSize < 7000 * 1024 ? false : true;//bug 775891
            }
        } catch (IllegalArgumentException e) {
            return false;
        }
        /** @} */
        return isEnough;
    }

    /**
     * check the storage space is enough
     * internal available space < 5%
     * external availabel space < 50K return false
     *
     * @return true is enough,or false
     */
    public static Map<String, String> getStorageInfo(String path) {
        Map<String, String> map = null;
        boolean isEnough = true;
        boolean isExternalUsed = StorageInfos.isInExternalSDCard(path);

        File savePath = null;
        if (isExternalUsed) {
            savePath = StorageInfos.getExternalStorageDirectory();
        } else {
            savePath = StorageInfos.getInternalStorageDirectory();
        }
        if (isInUsbCard(path)) {//bug 667315 the otg storage is full the pop is error
            savePath = getUSBStorage();
            Log.d("haveEnoughStorage", "sprd savepath=" + savePath);
        }

        //SPRD:Bug 629593 T-card set out into the internal storage, abnormal sound recorder settings bengin
        try {
            if (savePath != null) {
                map = new HashMap<String, String>();
                final StatFs stat = new StatFs(savePath.getPath());
                final long blockSize = stat.getBlockSize();
                final long availableBlocks = stat.getAvailableBlocks();
                long mAvailSize = availableBlocks * blockSize;
                map.put("availableBlocks", "" + (mAvailSize - 7000 * 1024));//bug 775891
                isEnough = mAvailSize < 7000 * 1024 ? false : true;//bug 775891
                map.put("isEnough", "" + isEnough);
            }
        } catch (IllegalArgumentException e) {
            Log.d("StorageInfos", "StatFs is exception");
        }
        //SPRD:Bug 629593 end
        return map;
    }

    private static boolean isInUsbCard(String path) {//bug 667315 the otg storage is full the pop is error
        if (path == null) {
            return false;
        }

        return getUSBStorage() != null && path.startsWith(getUSBStorage().getPath());
    }

    //bug 663417 when connect the otg u volume the first entry filelist is empty
    public static File getUSBStorage() {
        File[] usblist = StandardFrameworks.getInstances().getUsbStoragePath();
        if (usblist == null || usblist.length == 0
                || usblist[0] == null) {
            return null;
        }
        return usblist[0];
    }

    public static boolean isPathMounted(String path) {
        //bug 1193089 Dereference null return value (NULL_RETURNS)
        String internalPath = getInternalStorageDirectory().getPath();
        String externalPath = null;
        if(getExternalStorageDirectory() != null){
           externalPath = getExternalStorageDirectory().getPath();
        }
        if (externalPath != null && path.startsWith(externalPath)) {
            return isExternalStorageMounted();
        } else if ( internalPath != null && path.startsWith(internalPath)) {
            return isInternalStorageMounted();
        } else {
            return Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState(new File(path)));
        }
    }

    public static String getExternalStorageDefaultPath() {
        File path = getExternalStorageDirectory();
        if (path != null) {
            return path.getPath() + MUSIC_DIR + SprdRecorder.DEFAULT_STORE_SUBDIR;
        }
        return null;
    }

    public static String getInternalStorageDefaultPath() {
        File path = StorageInfos.getInternalStorageDirectory();
        if (path != null) {
            return path.getPath() + MUSIC_DIR + SprdRecorder.DEFAULT_STORE_SUBDIR;
        }
        return null;
    }

    public static String getOtgStorageDefaultPath() {
        File path = StorageInfos.getUSBStorage();
        if (path != null) {
            return path.getPath() + MUSIC_DIR + SprdRecorder.DEFAULT_STORE_SUBDIR;
        }
        return null;
    }

    public static String getAppInternalStorageDefaultPath(Context context) {
        String appPath = null;
        final List<File> androidMedias = new ArrayList<>();
        Collections.addAll(androidMedias, context.getExternalMediaDirs());
        for (File path : androidMedias) {
            if (path == null) {
                Log.e(TAG, "media path is null");
                continue;
            }
            Log.e(TAG, "path:"+path.getAbsolutePath());
            if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState()) &&
                    path.getAbsolutePath().contains("emulated")) {
                appPath = path.getAbsolutePath() + File.separator + "recordings";
            }

        }
        return appPath;
    }

    public static String getAppOwnDirSdcard(Context context) {
        String appPath = null;
        final List<File> androidMedias = new ArrayList<>();
        Collections.addAll(androidMedias, context.getExternalMediaDirs());
        for (File path: androidMedias) {
            if (path == null) {
                Log.e(TAG, "getAppOwnDirSdcard, Path is null");
                continue;
            }
            if (EnvironmentEx.getExternalStoragePathState().equals(Environment.MEDIA_MOUNTED) &&
                    path.getAbsolutePath().startsWith(EnvironmentEx.getExternalStoragePath().getAbsolutePath()) ) {
                appPath = path.getAbsolutePath() +File.separator + "recordings";
            }

        }
        return appPath;
    }

    public static String getAppOwnDirOtg(Context context, String otgIdPath) {
        String appPath = null;
        final List<File> androidMedias = new ArrayList<>();
        Collections.addAll(androidMedias, context.getExternalMediaDirs());
        android.util.Log.d(TAG, "getAppOwnDirOtg: otgIdPath:"+otgIdPath);
        for (File path: androidMedias) {
            if (path == null) {
                Log.e(TAG, "getAppOwnDirOtg, Path is null");
                continue;
            }
            android.util.Log.d(TAG, "getAppOwnDirOtg: path.getAbsolutePath():"+path.getAbsolutePath());
//            if (path.getAbsolutePath().startsWith(otgIdPath) ) {
            if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState()) &&
                    path.getAbsolutePath().contains("emulated")) {
                String internalPath = path.getAbsolutePath();
                android.util.Log.d(TAG, "getAppOwnDirOtg: internalPath:" + internalPath);
                int androidIndex = internalPath.indexOf("Android");
                android.util.Log.d(TAG, "getAppOwnDirOtg: androidIndex:" + androidIndex);
                String relativePath = internalPath.substring(androidIndex);
                android.util.Log.d(TAG, "getAppOwnDirOtg: relativePath:" + relativePath);
                appPath = otgIdPath + File.separator + relativePath + File.separator + "recordings";
                android.util.Log.d(TAG, "getAppOwnDirOtg: appPath:" + appPath);
            }
        }

        return appPath;
    }

    public static String getCallExternalPath() {
        String externalPath = "";
        File file = StandardFrameworks.getInstances().getExternalStoragePath();
        if (file != null) {
            externalPath = file.getPath();
        }
        return externalPath + CALL_RELATIVE_PATH;
    }

    // bug 1610581 check storage before set SDcard Storage
    public static boolean isSDcardAvalible(Context context) {
        File[] externalFilesDirs = context.getExternalFilesDirs(null);
        return isExternalStorageMounted() && externalFilesDirs.length > 1;
    }

}
