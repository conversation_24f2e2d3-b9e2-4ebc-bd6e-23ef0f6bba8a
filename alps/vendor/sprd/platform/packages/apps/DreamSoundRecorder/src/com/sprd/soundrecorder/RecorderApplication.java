/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.util.Log;

import com.sprd.soundrecorder.frameworks.StandardFrameworks;
import com.sprd.soundrecorder.service.RecordingService;
import com.sprd.soundrecorder.service.SprdRecorder;

public class RecorderApplication extends Application {
    private final static String TAG = RecorderApplication.class.getSimpleName();
    private static final int MODE_ULTRASAVING = 4;
    private static Context context;
    private static final String SUPPORT_ULTRASAVING = "IsSupportSuperPowerSavingMode";
    public static final String IS_ULTRASAVING_MODE = "com.android.soundrecorder.ULTRASAVING.MODE";
    public static final String POWEREX_SAVE_MODE_CHANGED_PERMISSION = "unisoc.permission.POWEREX_SAVE_MODE_CHANGED";
    @Override
    public void onCreate() {
        Log.d(TAG, "onCreate()");
        super.onCreate();
        context = getApplicationContext();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(StandardFrameworks.getInstances().getPowerSaveChangeAction());
        registerReceiver(mReceiver, intentFilter, POWEREX_SAVE_MODE_CHANGED_PERMISSION, null, Context.RECEIVER_EXPORTED);
    }

    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action.equals(StandardFrameworks.getInstances().getPowerSaveChangeAction())) {
                //bug1330219 stop and kill self when change to super power saving mode
                int mode = intent.getIntExtra(StandardFrameworks.getInstances().getPowerSaveMode(), 0);
                boolean allowed = StandardFrameworks.getInstances().isAllowedAppListInUltraSavingMode(context);
                Log.d(TAG, "mode:" + mode + ",allowed:" + allowed);
                Log.d(TAG, "XXX SprdRecorder.getRecordingStateState()" + SprdRecorder.getRecordingState());
                if (mode == MODE_ULTRASAVING && !allowed && SprdRecorder.getRecordingState()) {
                    Intent newIntent = new Intent(RecordingService.STOP_ACTION);
                    newIntent.putExtra(SUPPORT_ULTRASAVING, true);
                    sendBroadcast(newIntent);
                } else if (mode == MODE_ULTRASAVING && !allowed) {
                    Utils.killSelf(context);
                } else if (mode == MODE_ULTRASAVING) {
                    /* bug 1680271 avoid playing recording files in the ultrasaving mode @{ */
                    Intent newIntent = new Intent(IS_ULTRASAVING_MODE);
                    sendBroadcast(newIntent);
                    /* bug 1680271 }@ */
                }
            }
        }
    };

    public static Context getContext(){
        return context;
    }
}
