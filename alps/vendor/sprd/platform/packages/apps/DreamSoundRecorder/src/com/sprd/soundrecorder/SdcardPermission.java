/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import android.util.Log;

import com.android.soundrecorder.R;

import java.io.File;
import java.util.List;

public class SdcardPermission {
    private static final String TAG = SdcardPermission.class.getSimpleName();

    public static void requestDirectoryAccess(Activity activity, int requestCode, String path) {
        Intent intent = null;
        final List<StorageVolume> volumes = getVolumes(activity);
        if (volumes != null) {
            for (StorageVolume volume : volumes) {
                File volumePath = volume.getDirectory();
                if (volumePath.getAbsolutePath().contains("emulated") && path.contains("emulated")) {
                    Log.d(TAG, "requestDirectoryAccess, emulated");
                    intent = volume.createOpenDocumentTreeIntent();
                    break;
                } else if (!volumePath.getAbsolutePath().contains("emulated") && !path.contains("emulated")) {
                    Log.d(TAG, "requestDirectoryAccess, sd card");
                    intent = volume.createOpenDocumentTreeIntent();
                    break;
                }
            }
        }
        /*Bug 1387020 Coverity CID 1018781: explicitly null is dereferenced */
        if (intent != null) {
            activity.startActivityForResult(intent, requestCode);
        }
    }

    public static List<StorageVolume> getVolumes(Activity activity) {
        final StorageManager sm = (StorageManager) activity.getSystemService(Context.STORAGE_SERVICE);
        if (sm != null) {
            final List<StorageVolume> volumes = sm.getStorageVolumes();
            return volumes;
        }
        return null;
    }

    public static void getPersistableUriPermission(Uri uri, Intent data, Activity activity) {
        final int takeFlags = data.getFlags() & (Intent.FLAG_GRANT_READ_URI_PERMISSION |
                Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
        activity.getContentResolver().takePersistableUriPermission(uri, takeFlags);
    }

    public static boolean judgeRenameCallFile(String mFilePath) {
        boolean result = false;
        if (mFilePath.contains("voicecall")) {
            result = true;
        }
        return result;
    }

    /**
     * Display the dialog, and show no sd write permission on Aroidriod Q.
     *
     * @param context  context
     * @param listener listener
     */
    //bug:1094408 use new SAF issue to get SD write permission
    public static void showNoSdCallFileWritePermission(Context context, DialogInterface.OnClickListener listener) {
        new AlertDialog.Builder(context).setCancelable(false)
                .setMessage(R.string.document_request_confirm)
                .setPositiveButton(R.string.confirm, listener)
                .create()
                .show();
    }
}

