/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder.frameworks;

import android.content.Context;
import android.os.Environment;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import android.util.Log;

import com.sprd.soundrecorder.RecorderApplication;

import java.io.File;
import java.util.List;

public class EnvironmentEx {

    private static final String TAG = "EnvironmentEx";
    /**
     * replace EnvironmentEx.getInternalStoragePath()
     */
    public static File getInternalStoragePath() {
        return Environment.getExternalStorageDirectory();
    }

    /**
     * replace EnvironmentEx.getInternalStoragePathState()
     */
    public static String getInternalStoragePathState() {
        return Environment.getExternalStorageState();
    }

    /**
     * replace EnvironmentEx.getExternalStoragePath()
     */
    public static File getExternalStoragePath() {
        StorageManager storageManager = (StorageManager) RecorderApplication.getContext().getSystemService(Context.STORAGE_SERVICE);
        List<StorageVolume> volumes = storageManager.getStorageVolumes();
        for (StorageVolume volume : volumes) {
            File volumePath = volume.getDirectory();
            Log.i(TAG,"getExternalStoragePath volumePath : " + volumePath);
            if (volumePath != null && volume.isRemovable() &&
                Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState(volumePath))) {
                Log.i(TAG,"really createAccessIntent for : " + volumePath);
                return volumePath;
            }
        }
        return null;
    }

    /**
     * replace EnvironmentEx.getExternalStoragePathState()
     */
    public static String getExternalStoragePathState() {
        File volumePath = getExternalStoragePath();
        if (volumePath != null) {
            return Environment.getExternalStorageState(volumePath);
        }
        return Environment.MEDIA_UNKNOWN ;
    }
}
