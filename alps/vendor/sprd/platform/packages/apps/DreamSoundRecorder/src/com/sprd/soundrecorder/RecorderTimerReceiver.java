/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.sprd.soundrecorder.service.RecordingService;

/**
 * daojian.li1 bug 1935203
 */

public class RecorderTimerReceiver extends BroadcastReceiver {
    private final static String TAG = RecorderTimerReceiver.class.getSimpleName();
    public final static String PREPARE_RECORD = "StartRecord";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, " onReceive intent = " + intent);
        if (SettingActivity.TIMER_RECORD_START_ACTION.equals(intent.getAction())) {
            Intent startRecord = new Intent(context, RecordingService.class);
            startRecord.putExtra(PREPARE_RECORD, true);
            context.startService(startRecord);
        }
    }
}
