/*
 * SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
 * SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
 */
package com.sprd.soundrecorder.data;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.app.PendingIntent;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.net.Uri;
import android.preference.PreferenceManager;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.text.InputFilter;
import android.text.Spanned;
import android.util.Log;
import android.util.SparseArray;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import androidx.documentfile.provider.DocumentFile;

import com.android.soundrecorder.R;
import com.sprd.soundrecorder.EmojiUtil;
import com.sprd.soundrecorder.RecorderActivity;
import com.sprd.soundrecorder.SdcardPermission;
import com.sprd.soundrecorder.service.SprdRecorder;
import com.sprd.soundrecorder.StorageInfos;
import com.sprd.soundrecorder.Utils;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;


/**
 * Created by jian.xu on 2017/10/10.
 */

public class DataOpration {
    private final static String TAG = DataOpration.class.getSimpleName();
    public final static String SOUNDREOCRD_TYPE_AND_DTA = "soundrecord.type.and.data";
    public final static String SYSTEM_VERSION = "systemVersion";
    public final static String[] DATA_COLUMN = new String[]{
            MediaStore.Audio.Media._ID, // 0
            MediaStore.Audio.Media.DATA, //1
            MediaStore.Audio.Media.SIZE, //2
            MediaStore.Audio.Media.TITLE, //3
            MediaStore.Audio.Media.DISPLAY_NAME, //4
            MediaStore.Audio.Media.DATE_MODIFIED, //5
            MediaStore.Audio.Media.MIME_TYPE, //6
            MediaStore.Audio.Media.DURATION, //7
            MediaStore.Audio.Media.BOOKMARK //8
    };
    public static final String CALL_COMPOSER = "Call Record";
    public static final String COMPOSER = "FMSoundRecorder";
    public final static String PREF_SDCARD_URI = "pref_saved_sdcard_uri";
    public final static String PREF_INTERNAL_URI = "pref_saved_internal_uri";
    public final static String RECORD_RELATIVE_PATH = "Android/media/com.android.soundrecorder/recordings";
    public final static String RECORD_RELATIVE_PATH_MUSIC = "Music/recordings";
    public final static String CALL_INTERNAL_PATH = "/storage/emulated/0/Music/voicecall";
    private final static boolean SUPPORT_OTG = false;

    public enum RenamefailReason {
        OK,
        NO_FILE,
        SAME_NAME,
        OTHER
    }

    public final static int INPUT_MAX_LENGTH = 50;

    public final static int ANDROID_SDK_VERSION_DEFINE_Q = 29;

    public final static int REQUEST_SDCARD_PERMISSION_DELETE = 1;

    public final static int REQUEST_SDCARD_PERMISSION_RENAME = 2;

    private final static boolean SEARCH_WITH_PATH = false;

    private static String makeQueryString(Context context, boolean isCallData) {

        StringBuilder where = new StringBuilder();

//        if (android.os.Build.VERSION.SDK_INT > ANDROID_SDK_VERSION_DEFINE_Q) {
            SharedPreferences sharedPreferences = context.getSharedPreferences(SOUNDREOCRD_TYPE_AND_DTA, Context.MODE_PRIVATE);
            String systemVersion = sharedPreferences.getString(SYSTEM_VERSION, "0");
            Log.d(TAG, "makeQueryString, systemVersion:" + systemVersion);
            if (systemVersion.equals("0")) {
                if (isCallData) {
                    where.append("(");
                    where.append(MediaStore.Audio.Media.MIME_TYPE)
                            .append("='")
                            .append(SprdRecorder.AUDIO_AMR)
                            .append("') and (");

                    String callInternalPath = CALL_INTERNAL_PATH;
                    String callExternalPath = StorageInfos.getCallExternalPath();
                    if (callInternalPath != null) {
                        where.append(MediaStore.Audio.Media.DATA)
                                .append(" like '")
                                .append(callInternalPath);
                    }
                    if (callExternalPath != null) {
                        where.append("%' or ")
                                .append(MediaStore.Audio.Media.DATA)
                                .append(" like '")
                                .append(callExternalPath);
                    }
                    where.append("%')");
                } else {
                    where.append("(");
                    String[] mimetypes = SprdRecorder.getShowRecordMimeType();
                    for (int i = 0; i < mimetypes.length; i++) {
                        where.append(MediaStore.Audio.Media.MIME_TYPE)
                                .append("='")
                                .append(mimetypes[i]);
                        if (i < mimetypes.length - 1) {
                            where.append("' or ");
                        } else {
                            where.append("') and (");
                        }
                    }

                    //Todo: get call recorder default path
                    String defaultInternalPath;
                    String defaultExternalPath;
                    //bug 1395280 Place the recording file save path in the Music folder
                    if (StorageInfos.USE_MUSIC) {
                        defaultInternalPath = StorageInfos.getInternalStorageDefaultPath();
                        defaultExternalPath = StorageInfos.getExternalStorageDefaultPath();
                    } else {
                        defaultInternalPath = StorageInfos.getAppInternalStorageDefaultPath(context);
                        defaultExternalPath = StorageInfos.getAppOwnDirSdcard(context);
                    }
                    //bug 1331818 the otg devices is not displayed
                    if (SUPPORT_OTG) {
                        String defaultOtgPath = StorageInfos.getOtgStorageDefaultPath();
                        if (defaultOtgPath != null) {
                            where.append("%' or ")
                                    .append(MediaStore.Audio.Media.DATA)
                                    .append(" like '")
                                    .append(defaultOtgPath);
                        }
                    }

                    if (defaultInternalPath != null) {
                        where.append(MediaStore.Audio.Media.DATA)
                                .append(" like '")
                                .append(defaultInternalPath);
                    }

                    if (StorageInfos.isSDcardAvalible(context) && defaultExternalPath != null) {
                        where.append("%' or ")
                                .append(MediaStore.Audio.Media.DATA)
                                .append(" like '")
                                .append(defaultExternalPath);
                    }
                    where.append("%')");
                }
                // Bug 1581354 The recorder does not respond When the recording file moving
                where.append(" and ( ").
                        append(MediaStore.Audio.Media.SIZE)
                        .append(" > ? )");
            }
//        }
        else {
            where.append(MediaStore.Audio.Media.COMPOSER)
                    .append("='");
            if (isCallData) {
                where.append(CALL_COMPOSER);
            } else {
                where.append(COMPOSER);
            }
            where.append("'");

        }
        return where.toString();
    }

    public static ArrayList<RecorderItem> getRecorderData(final Context context, final boolean isCallData) {
        final ArrayList<RecorderItem> result = new ArrayList<RecorderItem>();
        //bug 1358064 getContext may be null in RecordItemFragment
        if (context == null) {
            return result;
        }
        String where = makeQueryString(context, isCallData);
        String[] selectionArgs = new String[]{"0"};
        Log.d(TAG, "the query where string is " + where);
        Cursor cursor = null;
        String sortOder = MediaStore.Audio.Media.DATE_ADDED + " desc";
        try {
            cursor = context.getContentResolver().query(
                    MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                    DATA_COLUMN, where, selectionArgs, sortOder);
            if (cursor != null) {
                Log.d(TAG, "query count:" + cursor.getCount());
                for (cursor.moveToFirst(); !cursor.isAfterLast(); cursor.moveToNext()) {
                    RecorderItem item = new RecorderItem();
                    item.setId(cursor.getLong(0));
                    item.setData(cursor.getString(1));
                    item.setSize(cursor.getLong(2));
                    item.setTitle(cursor.getString(3));
                    item.setDisplayName(cursor.getString(4));
                    item.setDateModify(cursor.getLong(5));
                    item.setMimeType(cursor.getString(6));
                    item.setDuration(cursor.getInt(7));
                    item.setTagNumber(cursor.getInt(8));
                    result.add(item);
                }
            }
        } catch (Exception e) {
            Log.v(TAG, "RecordingFileListTabUtils.CursorRecorderAdapter failed; E: " + e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        if (SEARCH_WITH_PATH) {
            SharedPreferences sharedPreferences = context.getSharedPreferences(SOUNDREOCRD_TYPE_AND_DTA, Context.MODE_PRIVATE);
            String systemVersion = sharedPreferences.getString(SYSTEM_VERSION, "0");
            if (systemVersion.equals("0") && result.size() > 0) {
                new Thread(new Runnable() {

                    @Override
                    public void run() {
                        ContentValues cv = new ContentValues();
                        if (isCallData) {
                            cv.put(MediaStore.Audio.Media.COMPOSER, CALL_COMPOSER);
                        } else {
                            cv.put(MediaStore.Audio.Media.COMPOSER, COMPOSER);
                        }
                        ContentResolver resolver = context.getContentResolver();
                        for (int i = 0; i < result.size(); i++) {
                            Uri uri = ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, result.get(i).getId());
                            Log.d(TAG, "query(): update COMPOSER to MediaStore, id=" + result.get(i).getId());
                            resolver.update(uri, cv, null, null);
                        }
                    }
                }).start();
                SharedPreferences.Editor edit = sharedPreferences.edit();
                edit.putString(SYSTEM_VERSION, android.os.Build.VERSION.RELEASE);
                edit.commit();
            }
        }
        return result;
    }

    public static RenamefailReason renameFile(Context context, RecorderItem data, String newtitle) {
        String oldDisplayName = data.getDisplayName();
        String extension = oldDisplayName.substring(oldDisplayName.lastIndexOf("."), oldDisplayName.length());
        String oldDataPath = data.getData();
        File oldFile = new File(oldDataPath);
        if (!oldFile.exists()) {
            Log.d(TAG, "old file is not exists");
            return RenamefailReason.NO_FILE;
        }
        String newDataPath = oldFile.getParent() + File.separator + newtitle + extension;
        File newFile = new File(newDataPath);
        String[] newDataPathDir = newDataPath.split("/");
        String newDataPathDirec = newDataPathDir[2].toLowerCase();
        if (newFile.exists() || newFile.isDirectory()) {
            return RenamefailReason.SAME_NAME;
        }

        if (newFile.getAbsolutePath().contains("voicecall")) {
            try {
                Uri recordUri = ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, data.getId());
                Uri documentUri = MediaStore.getDocumentUri(context, recordUri);
                Log.d(TAG, "recordUri: " + recordUri);
                Log.d(TAG, "documentUri: " + documentUri);
                Log.d(TAG, "dsiplayname: " + newtitle + extension);
                Uri uri = DocumentsContract.renameDocument(context.getContentResolver(), documentUri, newtitle + extension);
                Log.d(TAG, "uri: " + uri);

                return RenamefailReason.OK;

            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            try {
                WaveDataManager.rename(context, data.getTitle(), newtitle);
                ContentResolver resolver = context.getContentResolver();
                ContentValues values = new ContentValues(1);
                values.put(MediaStore.Audio.Media.DISPLAY_NAME, newtitle + extension);
                if (android.os.Build.VERSION.SDK_INT < ANDROID_SDK_VERSION_DEFINE_Q || (newDataPathDirec.equals("emulated"))) {
                    resolver.update(ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                            data.getId()),
                            values,
                            MediaStore.Audio.Media._ID + "=?",
                            new String[]{data.getId() + ""});
                } else {
                    resolver.update(MediaStore.Audio.Media.getContentUri(newDataPathDirec, data.getId()),
                            values,
                            MediaStore.Audio.Media._ID + "=?",
                            new String[]{data.getId() + ""});
                }
                return RenamefailReason.OK;
            } catch (Exception e) {
                android.util.Log.d(TAG, "renameFile: faild");
                e.printStackTrace();
            }
        }
        return RenamefailReason.OTHER;
    }

    public static void shareFiles(Context context, ArrayList<Uri> uris) {
        boolean multiple = uris.size() > 1;
        Intent intent = new Intent(multiple ? Intent.ACTION_SEND_MULTIPLE : Intent.ACTION_SEND);
        intent.setType("audio/*");

        ArrayList<android.net.Uri> fileProviderUris = new ArrayList<>();
        String[] projection = {MediaStore.Audio.Media.DATA, MediaStore.Audio.Media.DISPLAY_NAME};

        for (android.net.Uri uri : uris) {
            android.net.Uri finalUri = uri;

            Cursor cursor = context.getContentResolver().query(uri, projection, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                String filePath = cursor.getString(0);

                if (filePath != null) {
                    java.io.File file = new java.io.File(filePath);
                    if (file.exists()) {
                        try {
                            android.net.Uri fileProviderUri = androidx.core.content.FileProvider.getUriForFile(
                                    context,
                                    "com.android.soundrecorder.fileprovider",
                                    file);
                            finalUri = fileProviderUri;
                            Log.d("hmt---", "  Created FileProvider URI: " + fileProviderUri);
                        } catch (Exception e) {
                            Log.e("hmt---", "  Failed to create FileProvider URI", e);
                        }
                    }
                }
                cursor.close();
            }
            fileProviderUris.add(finalUri);
        }

        if (multiple) {
            intent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, fileProviderUris);
        } else {
            intent.putExtra(Intent.EXTRA_STREAM, fileProviderUris.get(0));
        }

        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        intent = Intent.createChooser(intent, context.getString(R.string.operate_share));
        intent.setFlags(intent.getFlags() |Intent.FLAG_ACTIVITY_SINGLE_TOP);
        context.startActivity(intent);
    }

    public static String getRecordFileId(Context context, String title, String requestedType) {
        Log.d(TAG, " title = " + title + ", requestedType = " + requestedType);
        if (title != null && context != null) {
            Uri uri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
            final String[] ids = new String[]{MediaStore.Audio.Media._ID,
                    MediaStore.Audio.Media.MIME_TYPE};
            final String where = MediaStore.Audio.Media.TITLE + "=? " + "AND "
                    + MediaStore.Audio.Media.MIME_TYPE + "=?";
            final String[] args = new String[]{title, requestedType};
            // Bug 1790644 capture abnormal pop-up of external storage during monkey test
            Cursor cursor;
            int id = -1;
            try{
                cursor = context.getContentResolver().query(uri, ids, where, args, null);
                if (cursor != null) {
                    if (cursor.moveToFirst() && !cursor.isAfterLast()) {
                        id = cursor.getInt(0);
                    }
                    cursor.close();
                }
            }catch (IllegalArgumentException e){
                Log.e(TAG, "getRecordFileId failed to query");
            }
            Log.d(TAG, "id = " + id);
            return String.valueOf(id);
        }
        return title;
    }

    private static int getRecorderPlaylistId(Context context) {
        Uri uri = MediaStore.Audio.Playlists.getContentUri("external");
        final String[] ids = new String[]{MediaStore.Audio.Playlists._ID};
        final String where = MediaStore.Audio.Playlists.NAME + "=?";
        final String[] args = new String[]{SprdRecorder.PLAYLIST_NAME};
        // Bug 1790644 capture abnormal pop-up of external storage during monkey test
        Cursor cursor;
        int id = -1;
        try{
            cursor = context.getContentResolver().query(uri, ids, where, args, null);
            if (cursor == null) {
                Log.v(TAG, "getRecorderPlaylistId returns null");
            }
            if (cursor != null) {
                cursor.moveToFirst();
                if (!cursor.isAfterLast()) {
                    id = cursor.getInt(0);
                }
                cursor.close();
            }
        }catch (IllegalArgumentException e){
            Log.e(TAG, "getRecorderPlaylistId failed to query");
        }
        return id;
    }

    private static Uri createPlaylist(Context context) {
        ContentValues cv = new ContentValues();
        cv.put(MediaStore.Audio.Playlists.NAME, "My recordings");
        Uri uri = context.getContentResolver().insert(MediaStore.Audio.Playlists.getContentUri("external"), cv);
        if (uri == null) {
            Toast.makeText(context, R.string.error_mediadb_new_record, Toast.LENGTH_SHORT)
                    .show();
        }
        return uri;
    }

    private static void addToPlaylist(Context context, int audioId, long playlistId) {
        String[] cols = new String[]{
                MediaStore.Audio.Media._ID
        };
        Uri uri = MediaStore.Audio.Playlists.Members.getContentUri("external", playlistId);
        // Bug 1790644 capture abnormal pop-up of external storage during monkey test
        Cursor cursor;
        try{
            cursor = context.getContentResolver().query(uri, cols, null, null, null);
            //cur.moveToFirst();
            if (cursor != null) {
                final int base = cursor.getCount();
                cursor.close();
                ContentValues values = new ContentValues();
                values.put(MediaStore.Audio.Playlists.Members.PLAY_ORDER, Integer.valueOf(base + audioId));
                values.put(MediaStore.Audio.Playlists.Members.AUDIO_ID, audioId);
                context.getContentResolver().insert(uri, values);
            }
        }catch (IllegalArgumentException e){
            Log.e(TAG, "addToPlaylist failed to query");
        }
    }

    public static Dialog showRenameConfirmDialog(final Context context,
                                                 final String fileFullPath,
                                                 final RecorderItem item) {

        if (fileFullPath==null){
            return null;
        }
        final Dialog alertDialog = new Dialog(context, 0);
        alertDialog.setContentView(R.layout.rename_save_dialog);
        Button okButton = alertDialog.findViewById(R.id.button_ok);
        Button cancelButton = alertDialog.findViewById(R.id.button_cancel);
        cancelButton.setText(R.string.rename_button);
        if (item != null) {
            ((TextView) alertDialog.findViewById(R.id.title)).setText(R.string.rename);
        }
        final EditText newName = alertDialog.findViewById(R.id.inputname);
        Log.d(TAG, "showRenameConfirmDialog, file:" + fileFullPath);
        String fileName = new File(fileFullPath).getName();
        int index = fileName.lastIndexOf(".");
        final String beforeName;
        final String mFileExtension;

        if (index > -1) {
            beforeName = fileName.substring(0, index);
            mFileExtension = fileName.substring(index);
        } else {
            return null;
        }

        final String parentPath = new File(fileFullPath).getParent();
        Log.d(TAG, "showRenameConfirmDialog,beforeName:" + beforeName + ",parentPath:" + parentPath + ",fileextension:" + mFileExtension);
        newName.setText(beforeName);
        newName.setLines(2);
        /* bug:957817 the limit toast show unnormal @{*/
        newName.setFilters(new InputFilter[]{new InputFilter.LengthFilter(DataOpration.INPUT_MAX_LENGTH) {
            Toast toast = null;

            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest,int dstart, int dend) {
                int keep = DataOpration.INPUT_MAX_LENGTH - (dest.length() - (dend - dstart));
                int totallength = dest.length() - (dend - dstart) + (end - start);
                if (keep <= 0) {
                    //bug 1118174 not accumulate toast display time
                    toast = Utils.showToastWithText(context, toast,
                            context.getString(R.string.input_length_overstep), Toast.LENGTH_LONG);
                    return "";// do not change the original character
                } else if (keep >= end - start) {
                    return null;
                } else {
                    // add for bug 967698
                    if (totallength > DataOpration.INPUT_MAX_LENGTH) {
                        toast = Utils.showToastWithText(context, toast,
                                context.getString(R.string.input_length_overstep), Toast.LENGTH_LONG);
                    }
                    //Additional character length is less than the length of the remaining,
                    //Only add additional characters are part of it
                    keep += start;
                    if (Character.isHighSurrogate(source.charAt(keep - 1))) {
                        --keep;
                        if (keep == start) {
                            return "";
                        }
                    }
                    return source.subSequence(start, keep);
               }
           }
        }
        /* @} */
        });
        newName.requestFocus();
        if (item == null) {
            alertDialog.setCancelable(false);
        }
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.show();

        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (context instanceof RecorderActivity) {
                    // bug906609 show wrong message when we abandon the recording of less than 1 sec
                    ((RecorderActivity) context).renameOprateCancle(true);
                    ((RecorderActivity) context).stopRecordNoSave();
                }
                alertDialog.dismiss();
            }
        });
        okButton.setOnClickListener(new View.OnClickListener() {
            Toast toast = null;

            @SuppressLint("StringFormatMatches")
            @Override
            public void onClick(View view) {

                String fileName = newName.getEditableText().toString().trim();
                String specialChar = Utils.fileNameFilter(fileName);
                String emojiChar = EmojiUtil.filterEmoji(fileName);
                if (fileName.isEmpty()) {
                    toast = Utils.showToastWithText(context,
                            toast, context.getString(R.string.filename_empty_error),
                            Toast.LENGTH_SHORT);
                    ;
                } else if (fileName.equals(beforeName)) {
                    if (item == null && context instanceof RecorderActivity) {
                        ((RecorderActivity) context).stopRecord();
                        alertDialog.dismiss();
                    } else {
                        toast = Utils.showToastWithText(context,
                                toast, context.getString(R.string.filename_is_not_modified),
                                Toast.LENGTH_SHORT);
                    }
                } else if (!specialChar.isEmpty()) {
                    toast = Utils.showToastWithText(context,
                            toast, context.getString(R.string.illegal_chars_of_filename),
                            Toast.LENGTH_SHORT);
                } else if (EmojiUtil.containsEmoji(fileName)) {
                    toast = Utils.showToastWithText(context,
                            toast, context.getString(R.string.special_char_exist, emojiChar),
                            Toast.LENGTH_SHORT);
                } else if (item == null && new File(parentPath + "/" + fileName + mFileExtension).exists()) {
                    toast = Utils.showToastWithText(context,
                            toast, context.getString(R.string.file_rename),
                            Toast.LENGTH_SHORT);
                } else {
                    if (item != null) {
                        if (context instanceof Activity && SdcardPermission.judgeRenameCallFile(fileFullPath)) {
                            SdcardPermission.requestDirectoryAccess((Activity) context, REQUEST_SDCARD_PERMISSION_RENAME, fileFullPath);
                        } else {
                            DataOpration.RenamefailReason result =
                                    DataOpration.renameFile(context, item,
                                            fileName);
                            if (result == DataOpration.RenamefailReason.OK) {
                                toast = Utils.showToastWithText(context,
                                        toast, context.getString(R.string.rename_save),
                                        Toast.LENGTH_SHORT);
                            } else if (result == DataOpration.RenamefailReason.SAME_NAME) {
                                /* Bug 1513084  recording file name is repeat @{ */
                                Log.d(TAG, "rename repeat:" + result);
                                toast = Utils.showToastWithText(context,
                                        toast, context.getString(R.string.file_rename),
                                        Toast.LENGTH_SHORT);
                                /* Bug 1513084 }@ */
                            } else {
                                Log.d(TAG, "rename fail:" + result);
                                toast = Utils.showToastWithText(context,
                                        toast, context.getString(R.string.rename_nosave),
                                        Toast.LENGTH_SHORT);
                            }
                        }
                    } else if (context instanceof RecorderActivity) {
                        ((RecorderActivity) context).stopRecord(fileName);
                    }
                    alertDialog.dismiss();
                }
            }

        });
        return alertDialog;
    }

    /* Bug913590, Use SAF to get SD write permission @{ */
    public static void deleteFileSAF(Uri uri, String mDataPath, Context context) {
        String[] s = DocumentsContract.getTreeDocumentId(uri).split(":");
        String RelativePath = mDataPath.substring(mDataPath.indexOf(s[0].toString()) + s[0].toString().length());

        Uri fileUri = DocumentsContract.buildDocumentUriUsingTree(uri, DocumentsContract.getTreeDocumentId(uri) + RelativePath);

        try {
            DocumentsContract.deleteDocument(context.getContentResolver(), fileUri);
        } catch (Exception e) {
            Log.e(TAG, "could not delete document ", e);
        }
    }

    private static boolean renameFileSAF(Context context, Uri sdRootUri, String oldDisplayName, File mNewFile) {

        DocumentFile mOldDocFile = getDocumentFileByName(context, sdRootUri, oldDisplayName);
        if (mOldDocFile != null) {
            mOldDocFile.renameTo(mNewFile.getName());
        }

        return true;
    }

    public static DocumentFile getDocumentFileByName(DocumentFile treeDocFile, String displayName) {
        DocumentFile document = treeDocFile;
        Log.d(TAG, "displayName:"+displayName);
        document = document.findFile(displayName);
        Log.d(TAG, "document==null:" + (document == null));
        return document;
    }

    public static DocumentFile getDocumentFileByName(Context context, Uri treeUri, String displayName) {
        DocumentFile treeDocument = DocumentFile.fromTreeUri(context, treeUri);

        return getDocumentFileByName(treeDocument, displayName);
    }

    private static Uri getSavedSdcardUri(Context context) {
        try {
            SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context);
            String uri = sharedPreferences.getString(PREF_SDCARD_URI, null);

            if (uri != null) {
                return Uri.parse(uri);
            }
        } catch(Throwable e) {
            Log.e(TAG, "getSavedSdcardUri error, Throwable: ",e);
        }

        return null;
    }
    /* Bug913590 }@ */


    public static Uri addToMediaDBNew(Context context, String recordAbsolutePath, String displayName, String requestedType) {
        ContentValues cv = new ContentValues();
        Uri result;
        try {
            cv.put(MediaStore.Audio.Media.MIME_TYPE, requestedType);
            cv.put(MediaStore.MediaColumns.DISPLAY_NAME, displayName);
            cv.put(MediaStore.MediaColumns.RELATIVE_PATH, StorageInfos.USE_MUSIC ? RECORD_RELATIVE_PATH_MUSIC : RECORD_RELATIVE_PATH);
            cv.put(MediaStore.MediaColumns.IS_PENDING, 1);
            Log.d(TAG, "Inserting audio record: " + cv.toString());

            if (recordAbsolutePath.contains("emulated")) {
                result = context.getContentResolver().insert(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, cv);
            } else {
                String[] filePathDir = recordAbsolutePath.split("/");
                String filePathDirec = filePathDir[2].toLowerCase();
                result = context.getContentResolver().insert(MediaStore.Audio.Media.getContentUri(filePathDirec), cv);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
        return result;
    }

    public static void updateMediaDB(Uri uri, Context context, long duration, long size, int tagNumber) {
        ContentValues cv = new ContentValues();
        cv.put(MediaStore.MediaColumns.SIZE, size);
        cv.put(MediaStore.Audio.Media.DURATION, duration);
        cv.put(MediaStore.Audio.Media.BOOKMARK, tagNumber);
        Log.d(TAG, "update audio record: " + cv.toString());
        context.getContentResolver().update(uri, cv, null, null);
    }

    public static String getTmpRecordPath(Uri uri, Context context) {
        String result = "";
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver().query(uri, DATA_COLUMN, null, null, null);
            if (cursor != null) {
                Log.d(TAG, "query count:" + cursor.getCount());
                for (cursor.moveToFirst(); !cursor.isAfterLast(); cursor.moveToNext()) {
                    result = cursor.getString(1);
                    Log.d(TAG, "result:" + result);
                }
            }
        } catch (Exception e) {
            Log.v(TAG, "getTmpRecordPath failed");
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return result;
    }

    public static String getRecordId(Context context, Uri uri) {
        Log.d(TAG, " uri = " + uri);
        if (uri != null && context != null) {
            final String[] ids = new String[]{MediaStore.Audio.Media._ID};
            Cursor cursor = context.getContentResolver().query(uri, ids, null, null, null);
            int id = -1;
            if (cursor != null) {
                if (cursor.moveToFirst() && !cursor.isAfterLast()) {
                    id = cursor.getInt(0);
                }
                cursor.close();
            }
            Log.d(TAG, "id = " + id);
            return String.valueOf(id);
        }
        return "";
    }


    public static IntentSender createDeleteIntentSender(Context context, SparseArray<RecorderItem> itemDatas) {
        Collection<Uri> uris = new ArrayList<Uri>();
        for (int i = 0; i < itemDatas.size(); i++) {
            long id = itemDatas.valueAt(i).getId();
            android.util.Log.d(TAG, "delete id: " + id);
            uris.add(ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, id));
        }
        PendingIntent intent = MediaStore.createDeleteRequest(context.getContentResolver(), uris);
        IntentSender sender = intent.getIntentSender();
        return sender;
    }

    /* Bug 1591695 Resume playing after file is removed  @{ */
    public static boolean isFilePathExist(Context context, final String path, final Uri uri) {
        if (uri != null && path != null) {
            final String where = MediaStore.Audio.Media.DATA + "=?";
            final String[] selectionArgs = new String[]{path};
            final String volumeName = path.split("/")[2];
            Log.d(TAG, "isFilePathExist: volumeName = " + volumeName);
            Cursor cursor = context.getContentResolver().query(uri, null, where, selectionArgs, null);
            try {
                if (cursor != null && cursor.getCount() != 0) {
                    if (!StorageInfos.isSDcardAvalible(context) && !"emulated".equals(volumeName)) {
                        return false;
                    } else {
                        return true;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (cursor != null) cursor.close();
            }
        }
        return false;
    }
    /* Bug 1591695 }@ */

}
