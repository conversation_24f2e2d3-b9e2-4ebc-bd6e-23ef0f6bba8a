<?xml version="1.0" encoding="UTF-8"?>
<!--
     SPDX-FileCopyrightText: 2016-2023 Unisoc (Shanghai) Technologies Co., Ltd
     SPDX-License-Identifier: LicenseRef-Unisoc-General-1.0
-->
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="b7433417-7268-43b4-bd57-37e144aadfbd" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DefaultGradleProjectSettings">
    <option name="isMigrated" value="true" />
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <projects_view />
      </state>
    </system>
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/SettingActivity.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="281">
              <caret line="427" column="12" selection-start-line="427" selection-start-column="12" selection-end-line="427" selection-end-column="12" />
              <folding>
                <element signature="e#7917#7918#0" expanded="true" />
                <element signature="e#7997#7998#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/MultiChooseActivity.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="281">
              <caret line="759" column="73" selection-start-line="759" selection-start-column="45" selection-end-line="759" selection-end-column="73" />
              <folding>
                <element signature="imports" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/SdcardPermission.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="91">
              <caret line="49" column="51" selection-start-line="49" selection-start-column="23" selection-end-line="49" selection-end-column="51" />
              <folding>
                <element signature="imports" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>timer_recording_detail</find>
      <find>the storage</find>
      <find>the stored</find>
      <find>the play</find>
      <find>playback_failed</find>
      <find>path_miss</find>
      <find>isExternalStorageMounted</find>
      <find>RingtoneManager</find>
      <find>recordings</find>
      <find>getA</find>
      <find>getInternalStoragePathState</find>
      <find>EnvironmentEx</find>
      <find>faild to mkdirs</find>
      <find>/storage/emulated/0/recordings</find>
      <find>DEFAULT_INTERNAL_PATH</find>
      <find>saf</find>
      <find>getInternalStorageDefaultPath</find>
      <find>start recording:</find>
      <find>SAF</find>
      <find>filePathDirec</find>
      <find>.add</find>
      <find>addToMediaDB</find>
      <find>orio</find>
      <find>judgeIncludeSdcardAndCallFile</find>
      <find>913590</find>
      <find>showNoSdCallFileWritePermission</find>
      <find>requestSdRootDirectoryAccess</find>
      <find>class</find>
      <find>startActivityForResult</find>
      <find>SettingActivity.this</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/res/values-om-rET/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-or/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-or-rIN/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-pa/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-pa-rIN/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-pl/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-pt-rBR/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-pt-rPT/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-ro/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-ru/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-sa-rIN/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-sat-rIN/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-sd-rIN/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-si/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-si-rLK/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-sk/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-sl/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-so-rSO/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-sq/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-sq-rAL/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-sr/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-sv/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-sw/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-ta/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-ta-rIN/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-te/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-te-rIN/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-th/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-ti-rET/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-tl/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-tr/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-ug/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-uk/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-ur/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-ur-rPK/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-zh-rCN/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-zh-rHK/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-zh-rTW/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-eu-rES/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-mni-rIN/strings.xml" />
        <option value="$PROJECT_DIR$/res/values/strings.xml" />
        <option value="$PROJECT_DIR$/res/values-vi/strings.xml" />
        <option value="$PROJECT_DIR$/src/com/sprd/soundrecorder/SdcardPermission.java" />
        <option value="$PROJECT_DIR$/src/com/sprd/soundrecorder/service/SprdRecorder.java" />
        <option value="$PROJECT_DIR$/src/com/sprd/soundrecorder/RecordPreviewPlayer.java" />
        <option value="$PROJECT_DIR$/src/com/sprd/soundrecorder/MultiChooseActivity.java" />
        <option value="$PROJECT_DIR$/src/com/sprd/soundrecorder/data/DataOpration.java" />
        <option value="$PROJECT_DIR$/src/com/sprd/soundrecorder/StorageInfos.java" />
        <option value="$PROJECT_DIR$/src/com/sprd/soundrecorder/frameworks/StandardFrameworks.java" />
        <option value="$PROJECT_DIR$/src/com/sprd/soundrecorder/frameworks/SprdFramewoks.java" />
        <option value="$PROJECT_DIR$/src/com/sprd/soundrecorder/SettingActivity.java" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds">
    <option name="y" value="23" />
    <option name="width" value="1920" />
    <option name="height" value="1017" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane" />
      <pane id="Scope" />
      <pane id="PackagesPane" />
      <pane id="AndroidView">
        <subPane>
          <expand>
            <path>
              <item name="DreamSoundRecorder" type="1abcf292:AndroidViewProjectNode" />
              <item name="DreamSoundRecorder" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="DreamSoundRecorder" type="1abcf292:AndroidViewProjectNode" />
              <item name="DreamSoundRecorder" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="DreamSoundRecorder" type="1abcf292:AndroidViewProjectNode" />
              <item name="DreamSoundRecorder" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="com" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="DreamSoundRecorder" type="1abcf292:AndroidViewProjectNode" />
              <item name="DreamSoundRecorder" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="com" type="462c0819:PsiDirectoryNode" />
              <item name="sprd" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="DreamSoundRecorder" type="1abcf292:AndroidViewProjectNode" />
              <item name="DreamSoundRecorder" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="com" type="462c0819:PsiDirectoryNode" />
              <item name="sprd" type="462c0819:PsiDirectoryNode" />
              <item name="soundrecorder" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="DreamSoundRecorder" type="1abcf292:AndroidViewProjectNode" />
              <item name="DreamSoundRecorder" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="com" type="462c0819:PsiDirectoryNode" />
              <item name="sprd" type="462c0819:PsiDirectoryNode" />
              <item name="soundrecorder" type="462c0819:PsiDirectoryNode" />
              <item name="data" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="DreamSoundRecorder" type="1abcf292:AndroidViewProjectNode" />
              <item name="DreamSoundRecorder" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="com" type="462c0819:PsiDirectoryNode" />
              <item name="sprd" type="462c0819:PsiDirectoryNode" />
              <item name="soundrecorder" type="462c0819:PsiDirectoryNode" />
              <item name="frameworks" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="DreamSoundRecorder" type="1abcf292:AndroidViewProjectNode" />
              <item name="DreamSoundRecorder" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="com" type="462c0819:PsiDirectoryNode" />
              <item name="sprd" type="462c0819:PsiDirectoryNode" />
              <item name="soundrecorder" type="462c0819:PsiDirectoryNode" />
              <item name="service" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="android.sdk.path" value="$USER_HOME$/Android/Sdk" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/../../../../../.." />
    <property name="settings.editor.selected.configurable" value="AndroidSdkUpdater" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b7433417-7268-43b4-bd57-37e144aadfbd" name="Default Changelist" comment="" />
      <created>1586496824540</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1586496824540</updated>
    </task>
    <servers />
  </component>
  <component name="ToolWindowManager">
    <frame x="0" y="23" width="1920" height="1017" extended-state="0" />
    <editor active="true" />
    <layout>
      <window_info id="Captures" order="0" weight="0.25" />
      <window_info content_ui="combo" id="Project" order="1" visible="true" weight="0.17358893" />
      <window_info id="Structure" order="2" side_tool="true" />
      <window_info id="Build Variants" order="3" side_tool="true" />
      <window_info id="Image Layers" order="4" />
      <window_info id="Designer" order="5" />
      <window_info id="Capture Tool" order="6" />
      <window_info id="Resources Explorer" order="7" />
      <window_info id="Favorites" order="8" side_tool="true" />
      <window_info anchor="bottom" id="Version Control" order="0" />
      <window_info anchor="bottom" id="TODO" order="1" />
      <window_info anchor="bottom" id="Terminal" order="2" />
      <window_info anchor="bottom" id="Event Log" order="3" side_tool="true" />
      <window_info anchor="bottom" id="Build" order="4" weight="0.32792208" />
      <window_info anchor="bottom" id="Find" order="5" weight="0.32932165" />
      <window_info anchor="right" id="Capture Analysis" order="0" />
      <window_info anchor="right" id="Gradle" order="1" />
      <window_info anchor="right" id="Theme Preview" order="2" />
      <window_info anchor="right" id="Palette&#9;" order="3" />
    </layout>
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/res/values-sv-keyshidden/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-sw/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="132" column="161" selection-start-line="132" selection-start-column="161" selection-end-line="132" selection-end-column="161" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-sw-keysexposed/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-sw-keyshidden/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-ta/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="63" column="125" selection-start-line="63" selection-start-column="125" selection-end-line="63" selection-end-column="125" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-ta-rIN/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="137" column="161" selection-start-line="137" selection-start-column="161" selection-end-line="137" selection-end-column="161" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-te/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="62" column="117" selection-start-line="62" selection-start-column="117" selection-end-line="62" selection-end-column="117" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-te-rIN/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="138" column="172" selection-start-line="138" selection-start-column="172" selection-end-line="138" selection-end-column="172" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-th/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="21" column="127" selection-start-line="21" selection-start-column="127" selection-end-line="21" selection-end-column="127" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-th-keysexposed/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-th-keyshidden/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-ti-rET/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="117" column="155" selection-start-line="117" selection-start-column="155" selection-end-line="117" selection-end-column="155" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-tl/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="39" column="123" selection-start-line="39" selection-start-column="123" selection-end-line="39" selection-end-column="123" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-tl-keysexposed/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-tl-keyshidden/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-tr/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="132" column="168" selection-start-line="132" selection-start-column="168" selection-end-line="132" selection-end-column="168" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-tr-keysexposed/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-tr-keyshidden/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-ug/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="63" column="117" selection-start-line="63" selection-start-column="117" selection-end-line="63" selection-end-column="117" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-uk/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="132" column="158" selection-start-line="132" selection-start-column="158" selection-end-line="132" selection-end-column="158" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-uk-keysexposed/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-uk-keyshidden/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-ur/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="55" column="117" selection-start-line="55" selection-start-column="117" selection-end-line="55" selection-end-column="117" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-ur-rPK/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="137" column="167" selection-start-line="137" selection-start-column="167" selection-end-line="137" selection-end-column="167" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-uz-rUZ/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-27" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-zh-rCN-keysexposed/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-zh-rCN-keyshidden/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-zh-rHK/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="132" column="145" selection-start-line="132" selection-start-column="145" selection-end-line="132" selection-end-column="145" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-zh-rTW/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="42" column="112" selection-start-line="42" selection-start-column="112" selection-end-line="42" selection-end-column="112" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-zh-rTW-keysexposed/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-zh-rTW-keyshidden/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-zu/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-27" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-zu-keyshidden/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="342">
          <caret line="18" lean-forward="true" selection-start-line="18" selection-end-line="18" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-zu-keysexposed/strings.xml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-eu-rES/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="278">
          <caret line="137" column="167" selection-start-line="137" selection-start-column="167" selection-end-line="137" selection-end-column="167" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-zh-rCN/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="297">
          <caret line="98" column="48" selection-start-line="98" selection-start-column="48" selection-end-line="98" selection-end-column="48" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-mni-rIN/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="114">
          <caret line="81" column="57" lean-forward="true" selection-start-line="81" selection-start-column="57" selection-end-line="81" selection-end-column="57" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values-vi/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="506">
          <caret line="144" column="20" lean-forward="true" selection-start-line="144" selection-start-column="20" selection-end-line="144" selection-end-column="20" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/res/values/strings.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="281">
          <caret line="158" column="87" selection-start-line="158" selection-start-column="35" selection-end-line="158" selection-end-column="87" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/RecordFilePlayActivity.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="357">
          <caret line="618" column="41" selection-start-line="618" selection-start-column="24" selection-end-line="618" selection-end-column="41" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/data/WaveDataManager.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="309">
          <caret line="46" column="15" lean-forward="true" selection-start-line="46" selection-start-column="15" selection-end-line="46" selection-end-column="58" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/RecordPreviewPlayer.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="632">
          <caret line="88" column="31" lean-forward="true" selection-start-line="88" selection-start-column="31" selection-end-line="88" selection-end-column="31" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/service/SprdRecorder.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1330">
          <caret line="96" column="44" selection-start-line="96" selection-start-column="31" selection-end-line="96" selection-end-column="44" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/StorageInfos.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-3356">
          <caret line="48" column="19" selection-start-line="48" selection-start-column="15" selection-end-line="48" selection-end-column="19" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/frameworks/StandardFrameworks.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="262">
          <caret line="73" selection-start-line="73" selection-end-line="73" />
          <folding>
            <element signature="e#2003#2004#0" expanded="true" />
            <element signature="e#2063#2064#0" expanded="true" />
            <element signature="e#2107#2108#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/frameworks/SprdFramewoks.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="57">
          <caret line="79" column="49" lean-forward="true" selection-start-line="79" selection-start-column="49" selection-end-line="79" selection-end-column="49" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/data/DataOpration.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="319">
          <caret line="339" column="36" selection-start-line="339" selection-start-column="36" selection-end-line="339" selection-end-column="36" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/service/RecordingService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="290">
          <caret line="796" column="17" selection-start-line="796" selection-start-column="17" selection-end-line="796" selection-end-column="17" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/MultiChooseActivity.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="281">
          <caret line="759" column="73" selection-start-line="759" selection-start-column="45" selection-end-line="759" selection-end-column="73" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/SdcardPermission.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="91">
          <caret line="49" column="51" selection-start-line="49" selection-start-column="23" selection-end-line="49" selection-end-column="51" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/com/sprd/soundrecorder/SettingActivity.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="281">
          <caret line="427" column="12" selection-start-line="427" selection-start-column="12" selection-end-line="427" selection-end-column="12" />
          <folding>
            <element signature="e#7917#7918#0" expanded="true" />
            <element signature="e#7997#7998#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
</project>