//
// Copyright (C) 2023 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

package {
    default_team: "trendy_team_capture_and_share",
    default_applicable_licenses: ["Android-Apache-2.0"],
}

android_test {
    name: "IntentResolver-tests-integration",
    srcs: [
        "src/**/*.java",
        "src/**/*.kt",
    ],

    libs: [
        "android.test.runner",
        "android.test.base",
        "framework",
    ],
    resource_dirs: ["res"],
    test_config: "AndroidTest.xml",
    static_libs: [
        "androidx.test.runner",
        "IntentResolver-core",
        "IntentResolver-tests-shared",
        "junit",
        "truth",
        "truth-java8-extension",
    ],
    test_suites: ["general-tests"],
}
