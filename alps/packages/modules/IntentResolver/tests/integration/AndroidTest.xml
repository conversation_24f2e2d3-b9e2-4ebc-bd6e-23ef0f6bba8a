<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<configuration description="Run IntentResolver Tests.">
    <target_preparer class="com.android.tradefed.targetprep.TestAppInstallSetup">
        <option name="cleanup-apks" value="true" />
        <option name="test-file-name" value="IntentResolver-tests-integration.apk" />
    </target_preparer>

    <target_preparer class="com.android.tradefed.targetprep.DeviceSetup">
        <option name="screen-always-on" value="on" />
    </target_preparer>

    <target_preparer class="com.android.tradefed.targetprep.RunCommandTargetPreparer">
        <option name="run-command" value="input keyevent KEYCODE_WAKEUP" />
        <option name="run-command" value="wm dismiss-keyguard" />
    </target_preparer>

    <target_preparer class="com.android.tradefed.targetprep.RootTargetPreparer"/>

    <test class="com.android.tradefed.testtype.AndroidJUnitTest" >
        <option name="package" value="com.android.intentresolver.tests.integration" />
        <option name="runner" value="androidx.test.runner.AndroidJUnitRunner" />
        <option name="hidden-api-checks" value="false"/>
    </test>
</configuration>
