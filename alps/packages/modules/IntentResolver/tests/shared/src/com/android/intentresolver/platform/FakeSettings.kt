/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.intentresolver.platform

/**
 * Creates a Settings instance with predefined values:
 *
 *     val settings: SecureSettings = fakeSettings {
 *         putString("stringValue", "example")
 *         putInt("intValue", 42)
 *     }
 */
inline fun <reified T : SettingsProxy> fakeSettings(block: SettingsProxy.() -> Unit): T {
    return FakeSettings(mutableMapOf()).apply(block) as T
}

/** A memory-only implementation of [SettingsProxy]. */
class FakeSettings(
    private val map: MutableMap<String, String>,
) : GlobalSettings, SecureSettings, SystemSettings {
    constructor() : this(mutableMapOf())

    override fun getStringOrNull(name: String): String? = map[name]

    override fun putString(name: String, value: String): Boolean {
        map[name] = value
        return true
    }
}
