<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
              package="com.android.intentresolver.tests">

    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL"/>
    <uses-permission android:name="android.permission.QUERY_USERS"/>
    <uses-permission android:name="android.permission.READ_CLIPBOARD_IN_BACKGROUND"/>
    <uses-permission android:name="android.permission.WRITE_DEVICE_CONFIG"/>
    <uses-permission android:name="android.permission.READ_DEVICE_CONFIG" />

    <application android:name="dagger.hilt.android.testing.HiltTestApplication">
        <uses-library android:name="android.test.runner" />
        <activity android:name="com.android.intentresolver.ChooserWrapperActivity" />
        <activity android:name="com.android.intentresolver.ResolverWrapperActivity" />
        <activity android:name="com.android.intentresolver.ChooserWrapperActivity" />
        <activity android:name="com.android.intentresolver.ResolverWrapperActivity" />
        <provider
            android:authorities="com.android.intentresolver.tests"
            android:name="com.android.intentresolver.TestContentProvider"
            android:grantUriPermissions="true" />
    </application>

    <instrumentation android:name="androidx.test.runner.AndroidJUnitRunner"
            android:targetPackage="com.android.intentresolver.tests">
    </instrumentation>

</manifest>
