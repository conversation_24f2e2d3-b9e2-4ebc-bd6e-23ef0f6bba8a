//
// Copyright (C) 2023 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

package {
    default_team: "trendy_team_capture_and_share",
    default_applicable_licenses: ["Android-Apache-2.0"],
}

android_test {
    name: "IntentResolver-tests-activity",
    manifest: "AndroidManifest.xml",
    srcs: [
        "src/**/*.java",
        "src/**/*.kt",
    ],

    libs: [
        "android.test.runner",
        "android.test.base",
        "android.test.mock",
        "framework",
        "framework-res",
    ],

    resource_dirs: ["res"],
    test_config: "AndroidTest.xml",
    static_libs: [
        "androidx.test.core",
        "androidx.test.ext.junit",
        "androidx.test.ext.truth",
        "androidx.test.espresso.contrib",
        "androidx.test.espresso.core",
        "androidx.test.rules",
        "androidx.test.runner",
        "androidx.lifecycle_lifecycle-common-java8",
        "androidx.lifecycle_lifecycle-extensions",
        "androidx.lifecycle_lifecycle-runtime-testing",
        "hilt_android_testing",
        "IntentResolver-core",
        "IntentResolver-tests-shared",
        "junit",
        "kotlinx_coroutines_test",
        "mockito-target-minus-junit4",
        "mockito-kotlin-nodeps",
        "testables",
        "truth",
        "truth-java8-extension",
        "flag-junit",
        "platform-test-annotations",
    ],
    plugins: ["dagger2-compiler"],
    test_suites: ["general-tests"],
    sdk_version: "core_platform",
    min_sdk_version: "current",
    target_sdk_version: "current",
    platform_apis: true,
}
