<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!-- This is based off of the chevron_right icon seen elsewhere, but the padding
     on the right side of the icon has been removed (so the chevron can align with
     other UI elements. This was done by reducing the viewport width to the maximum
     extent of the path itself. -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
        android:width="16dp"
        android:height="24dp"
        android:viewportWidth="16"
        android:viewportHeight="24"
        android:autoMirrored="true"
        android:tint="?androidprv:attr/materialColorOnSurface">
    <path
        android:fillColor="@android:color/white"
        android:pathData="M10,4.5L8.59,5.91 13.17,10.5l-4.58,4.59L10,16.5l6,-6 -6,-6z"/>
</vector>
