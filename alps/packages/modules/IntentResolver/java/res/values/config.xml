<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2009, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- These resources are around just to allow their values to be customized
     for different hardware and product builds.  Do not translate.

     NOTE: The naming convention is "config_camelCaseValue". Some legacy
     entries do not follow the convention, but all new entries should. -->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- The duration (in milliseconds) of a short animation. -->
    <integer name="config_shortAnimTime">@android:integer/config_shortAnimTime</integer>

    <!-- Sharesheet: define a max number of targets per application for new shortcuts-based direct share introduced in Q -->
    <integer name="config_maxShortcutTargetsPerApp">@*android:integer/config_maxShortcutTargetsPerApp</integer>

    <!-- Component name that accepts ACTION_SEND intents for nearby (proximity-based) sharing.
         Used by ChooserActivity. -->
    <string translatable="false" name="config_defaultNearbySharingComponent">@*android:string/config_defaultNearbySharingComponent</string>

    <!-- Chooser image editing activity.  Must handle ACTION_EDIT image/png intents.
         If omitted, image editing will not be offered via Chooser.
         This name is in the ComponentName flattened format (package/class) [DO NOT TRANSLATE]  -->
    <string name="config_systemImageEditor" translatable="false">@*android:string/config_systemImageEditor</string>

    <integer name="config_chooser_max_targets_per_row">5</integer>
</resources>
