<!--
  ~ Copyright (C) 2022 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<resources xmlns:android="http://schemas.android.com/apk/res/android">
    <dimen name="resolver_max_width">480dp</dimen>

    <!-- chooser/resolver (sharesheet) spacing -->
    <dimen name="chooser_action_corner_radius">28dp</dimen>
    <dimen name="chooser_action_horizontal_margin">2dp</dimen>
    <dimen name="chooser_width">450dp</dimen>
    <dimen name="chooser_corner_radius">28dp</dimen>
    <dimen name="chooser_corner_radius_small">14dp</dimen>
    <dimen name="chooser_row_text_option_translate">25dp</dimen>
    <dimen name="chooser_edge_margin_thin">8dp</dimen>
    <dimen name="chooser_edge_margin_normal">16dp</dimen>
    <dimen name="chooser_edge_margin_normal_half">8dp</dimen>
    <dimen name="chooser_preview_image_font_size">20sp</dimen>
    <dimen name="chooser_preview_image_border">1dp</dimen>
    <dimen name="chooser_preview_image_width">120dp</dimen>
    <dimen name="chooser_preview_image_max_dimen">200dp</dimen>
    <dimen name="chooser_header_scroll_elevation">4dp</dimen>
    <dimen name="chooser_max_collapsed_height">288dp</dimen>
    <dimen name="chooser_icon_size">56dp</dimen>
    <dimen name="chooser_badge_size">22dp</dimen>
    <dimen name="chooser_headline_text_size">18sp</dimen>
    <dimen name="chooser_grid_target_name_text_size">12sp</dimen>
    <dimen name="chooser_grid_activity_name_text_size">12sp</dimen>
    <dimen name="resolver_icon_size">32dp</dimen>
    <dimen name="resolver_button_bar_spacing">0dp</dimen>
    <dimen name="resolver_badge_size">18dp</dimen>
    <dimen name="resolver_icon_margin">8dp</dimen>
    <dimen name="resolver_small_margin">18dp</dimen>
    <dimen name="resolver_edge_margin">24dp</dimen>
    <dimen name="resolver_elevation">1dp</dimen>
    <dimen name="resolver_max_collapsed_height">192dp</dimen>
    <dimen name="resolver_max_collapsed_height_with_tabs">268dp</dimen>
    <dimen name="resolver_max_collapsed_height_with_default">144dp</dimen>
    <dimen name="resolver_max_collapsed_height_with_default_with_tabs">300dp</dimen>
    <dimen name="resolver_tab_text_size">14sp</dimen>
    <dimen name="resolver_title_padding_bottom">0dp</dimen>
    <dimen name="resolver_empty_state_container_padding_top">48dp</dimen>
    <dimen name="resolver_empty_state_container_padding_bottom">8dp</dimen>
    <dimen name="resolver_profile_tab_margin">4dp</dimen>
    <dimen name="chooser_action_view_icon_size">22dp</dimen>
    <dimen name="chooser_action_view_text_size">12sp</dimen>
    <dimen name="chooser_action_margin">0dp</dimen>
    <dimen name="modify_share_text_toggle_max_width">150dp</dimen>
    <dimen name="chooser_view_spacing">16dp</dimen>

    <!-- Note that the values in this section are for landscape phones. For screen configs taller
         than 480dp, the values are set in values-h480dp/dimens.xml -->
    <dimen name="chooser_preview_width">412dp</dimen>
    <dimen name="chooser_preview_image_height_tall">124dp</dimen>
    <dimen name="grid_padding">8dp</dimen>
    <dimen name="width_text_image_preview_size">46dp</dimen>
    <!-- END SECTION -->
</resources>
