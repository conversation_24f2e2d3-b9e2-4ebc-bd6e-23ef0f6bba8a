<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2006 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- Formatting note: terminate all comments with a period, to avoid breaking
     the documentation output. To suppress comment lines from the documentation
     output, insert an eat-comment element after the comment lines.
-->

<resources>
    <declare-styleable name="ResolverDrawerLayout">
        <attr name="android:maxWidth" />
        <attr name="maxCollapsedHeight" format="dimension" />
        <attr name="maxCollapsedHeightSmall" format="dimension" />
        <!-- Whether the Drawer should be positioned at the top rather than at the bottom. -->
        <attr name="showAtTop" format="boolean" />
        <!-- By default `ResolverDrawerLayout`’s children views with `layout_ignoreOffset` property
             set to true have a fixed position in the layout that won’t be affected by the drawer’s
             movements. This property alternates that behavior. It specifies a child view’s id that
             will push all ignoreOffset siblings below it when the drawer is moved i.e. setting the
             top limit the ignoreOffset elements. -->
        <attr name="ignoreOffsetTopLimit" format="reference" />
        <!-- Specifies whether ResolverDrawerLayout should use an alternative nested fling logic
        adjusted for the scrollable preview feature.
        Controlled by the flag com.android.intentresolver.Flags#FLAG_SCROLLABLE_PREVIEW.
        -->
        <attr name="useScrollablePreviewNestedFlingLogic" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ResolverDrawerLayout_LayoutParams">
        <attr name="layout_alwaysShow" format="boolean" />
        <attr name="layout_ignoreOffset" format="boolean" />
        <attr name="android:layout_gravity" />
        <attr name="layout_hasNestedScrollIndicator" format="boolean" />
        <attr name="layout_maxHeight" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="RoundedRectImageView">
        <attr name="radius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="ScrollableImagePreviewView">
        <attr name="itemInnerSpacing" format="dimension" />
        <attr name="itemOuterSpacing" format="dimension" />
        <attr name="maxWidthHint" format="dimension" />
    </declare-styleable>
</resources>
