<!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/chooser_preview_image_height_tall">

    <com.android.intentresolver.widget.RoundedRectImageView
            android:id="@+id/image"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            app:layout_constraintDimensionRatio="W,1:1"
            android:layout_alignParentTop="true"
            android:adjustViewBounds="false"
            android:scaleType="centerCrop"
            app:radius="@dimen/chooser_corner_radius_small"
            android:importantForAccessibility="no" />

    <FrameLayout
        android:id="@+id/badge_frame"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="@+id/image"
        app:layout_constraintEnd_toEndOf="@+id/image"
        app:layout_constraintTop_toTopOf="@+id/image"
        app:layout_constraintBottom_toBottomOf="@+id/image"
        android:background="@drawable/content_preview_badge_bg"
        android:importantForAccessibility="noHideDescendants">

        <ImageView
            android:id="@+id/badge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="center"
            android:layout_marginEnd="6dp"
            android:layout_marginTop="6dp"
            android:tint="@android:color/white"
            android:layout_gravity="top|end" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/edit"
        android:layout_width="48dp"
        android:layout_height="48dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/edit_action_background"
        android:drawableTint="?androidprv:attr/materialColorSecondaryFixed"
        android:contentDescription="@string/screenshot_edit"
        android:visibility="gone"
        >
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:padding="4dp"
            android:tint="?androidprv:attr/materialColorOnSecondaryFixed"
            android:src="@androidprv:drawable/ic_screenshot_edit"
            />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
