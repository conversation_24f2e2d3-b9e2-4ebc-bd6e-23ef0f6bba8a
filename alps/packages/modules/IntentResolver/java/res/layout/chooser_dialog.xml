<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2020 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    android:id="@+id/chooser_dialog_content"
    android:background="@drawable/chooser_dialog_background"
    android:orientation="vertical"
    android:paddingBottom="8dp"
    android:paddingTop="8dp"
    android:layout_width="240dp"
    android:layout_height="wrap_content">

    <LinearLayout
        android:gravity="start|center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:minHeight="56dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView android:id="@android:id/icon"
                   android:layout_marginEnd="16dp"
                   android:layout_width="24dp"
                   android:layout_height="24dp"/>

        <TextView android:id="@android:id/title"
                  android:textSize="16sp"
                  android:textColor="?android:attr/textColorPrimary"
                  android:textAppearance="@android:style/TextAppearance.DeviceDefault.WindowTitle"
                  android:text="App name"
                  android:lines="1"
                  android:ellipsize="end"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"/>

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        xmlns:app="http://schemas.android.com/apk/res-auto"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:id="@androidprv:id/listContainer"
        android:overScrollMode="never"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

</LinearLayout>
