<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2020 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
              android:background="?android:attr/selectableItemBackground"
              android:clickable="true"
              android:paddingStart="16dp"
              android:paddingEnd="16dp"
              android:orientation="horizontal"
              android:gravity="start|center_vertical"
              android:minHeight="56dp"
              android:layout_width="match_parent"
              android:layout_height="wrap_content">

    <ImageView android:id="@android:id/icon"
               android:alpha="0.54"
               android:tint="?android:attr/textColorPrimary"
               android:layout_marginEnd="16dp"
               android:layout_width="24dp"
               android:layout_height="24dp"/>

    <TextView android:id="@androidprv:id/text"
              android:textAppearance="?android:attr/textAppearanceSmall"
              android:textColor="?android:attr/textColorPrimary"
              android:textSize="16sp"
              android:maxLines="2"
              android:ellipsize="end"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"/>

</LinearLayout>
