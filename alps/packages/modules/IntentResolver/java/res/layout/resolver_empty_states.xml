<!--
  ~ Copyright (C) 2020 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    android:id="@androidprv:id/resolver_empty_state"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:visibility="gone"
    android:paddingStart="24dp"
    android:paddingEnd="24dp">
    <RelativeLayout
        android:id="@androidprv:id/resolver_empty_state_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/resolver_empty_state_container_padding_top"
        android:paddingBottom="@dimen/resolver_empty_state_container_padding_bottom"
        android:gravity="center_horizontal">
        <TextView
            android:id="@androidprv:id/resolver_empty_state_title"
            android:layout_below="@androidprv:id/resolver_empty_state_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@androidprv:string/config_headlineFontFamilyMedium"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="18sp"
            android:lineHeight="24sp"
            android:gravity="center_horizontal"
            android:layout_centerHorizontal="true" />
        <TextView
            android:id="@androidprv:id/resolver_empty_state_subtitle"
            android:layout_below="@androidprv:id/resolver_empty_state_title"
            android:layout_marginTop="16dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="14sp"
            android:lineHeight="20sp"
            android:gravity="center_horizontal"
            android:layout_centerHorizontal="true" />
        <Button
            android:id="@androidprv:id/resolver_empty_state_button"
            android:layout_below="@androidprv:id/resolver_empty_state_subtitle"
            android:layout_marginTop="16dp"
            android:text="@string/resolver_switch_on_work"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="12dp"
            android:fontFamily="@androidprv:string/config_headlineFontFamilyMedium"
            android:textSize="14sp"
            android:textColor="?android:attr/textColorPrimary"
            android:layout_centerHorizontal="true"
            android:background="@drawable/chooser_action_button_bg"
            />
        <ProgressBar
            android:id="@androidprv:id/resolver_empty_state_progress"
            style="@android:style/Widget.Material.Light.ProgressBar"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:indeterminate="true"
            android:layout_alignTop="@androidprv:id/resolver_empty_state_icon"
            android:layout_alignBottom="@androidprv:id/resolver_empty_state_button"
            android:layout_centerHorizontal="true"
            android:layout_below="@androidprv:id/resolver_empty_state_subtitle"
            android:indeterminateTint="?android:attr/colorAccent"/>
        <TextView
            android:id="@android:id/empty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/noApplications"
            android:textColor="?androidprv:attr/materialColorOnSurfaceVariant"
            android:padding="@dimen/chooser_edge_margin_normal"
            android:gravity="center"/>
    </RelativeLayout>
</RelativeLayout>
