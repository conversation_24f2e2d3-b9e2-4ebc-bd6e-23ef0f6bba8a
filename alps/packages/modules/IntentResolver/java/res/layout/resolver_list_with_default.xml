<?xml version="1.0" encoding="utf-8"?>
<!--
/*
* Copyright 2014, The Android Open Source Project
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/
-->
<com.android.intentresolver.widget.ResolverDrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:maxWidth="@dimen/resolver_max_width"
    app:maxCollapsedHeight="@dimen/resolver_max_collapsed_height_with_default"
    android:id="@androidprv:id/contentPanel">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_alwaysShow="true"
        android:orientation="vertical"
        android:background="@drawable/bottomsheet_background"
        android:paddingTop="@dimen/resolver_small_margin"
        android:elevation="@dimen/resolver_elevation">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/resolver_edge_margin"
            android:paddingEnd="@dimen/resolver_edge_margin">
            <ImageView
                android:id="@android:id/icon"
                android:layout_width="@dimen/resolver_icon_size"
                android:layout_height="@dimen/resolver_icon_size"
                android:layout_gravity="start|top"
                android:layout_marginStart="@dimen/resolver_icon_margin"
                android:src="@drawable/resolver_icon_placeholder"
                android:scaleType="fitCenter" />

            <TextView
                android:id="@android:id/title"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/resolver_icon_margin"
                android:textColor="?android:attr/textColorPrimary"
                android:fontFamily="@androidprv:string/config_headlineFontFamilyMedium"
                android:textSize="16sp"
                android:gravity="start|center_vertical"
                android:paddingEnd="16dp" />

            <LinearLayout
                android:id="@androidprv:id/profile_button"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="4dp"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:focusable="true"
                android:visibility="gone"
                style="?android:attr/borderlessButtonStyle">

                <ImageView
                    android:id="@android:id/icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="start|center_vertical"
                    android:layout_marginEnd="?android:attr/listPreferredItemPaddingEnd"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="12dp"
                    android:scaleType="fitCenter" />

                <TextView
                    android:id="@android:id/text1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|center_vertical"
                    android:layout_marginEnd="?android:attr/listPreferredItemPaddingEnd"
                    android:textAppearance="?android:attr/textAppearanceButton"
                    android:textColor="?android:attr/textColorPrimary"
                    android:minLines="1"
                    android:maxLines="1"
                    android:ellipsize="marquee" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@androidprv:id/button_bar"
            android:visibility="gone"
            style="?android:attr/buttonBarStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_alwaysShow="true"
            android:gravity="end|center_vertical"
            android:orientation="horizontal"
            android:layoutDirection="locale"
            android:measureWithLargestChild="true"
            android:paddingTop="@dimen/resolver_button_bar_spacing"
            android:paddingBottom="@dimen/resolver_button_bar_spacing"
            android:paddingStart="@dimen/resolver_edge_margin"
            android:paddingEnd="@dimen/resolver_small_margin"
            android:elevation="@dimen/resolver_elevation">

            <Button
                android:id="@androidprv:id/button_once"
                android:layout_width="wrap_content"
                android:layout_gravity="start"
                android:maxLines="2"
                style="?android:attr/buttonBarButtonStyle"
                android:fontFamily="@androidprv:string/config_headlineFontFamilyMedium"
                android:layout_height="wrap_content"
                android:enabled="false"
                android:textAllCaps="false"
                android:text="@string/activity_resolver_use_once"
                android:onClick="onButtonClick" />

            <Button
                android:id="@androidprv:id/button_always"
                android:layout_width="wrap_content"
                android:layout_gravity="end"
                android:maxLines="2"
                style="?android:attr/buttonBarButtonStyle"
                android:fontFamily="@androidprv:string/config_headlineFontFamilyMedium"
                android:layout_height="wrap_content"
                android:enabled="false"
                android:textAllCaps="false"
                android:text="@string/activity_resolver_use_always"
                android:onClick="onButtonClick" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:id="@androidprv:id/divider"
        app:layout_alwaysShow="true"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/colorBackground"
        android:foreground="?android:attr/dividerVertical" />

    <FrameLayout
        android:id="@androidprv:id/stub"
        app:layout_alwaysShow="true"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?android:attr/colorBackground"/>

    <TabHost
        app:layout_alwaysShow="true"
        android:id="@androidprv:id/profile_tabhost"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:background="?android:attr/colorBackground">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TabWidget
                android:id="@android:id/tabs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone">
            </TabWidget>
            <View
                android:id="@androidprv:id/resolver_tab_divider"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="?android:attr/colorBackground"
                android:foreground="?android:attr/dividerVertical"/>
            <FrameLayout
                android:id="@android:id/tabcontent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <com.android.intentresolver.ResolverViewPager
                    android:id="@androidprv:id/profile_pager"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </FrameLayout>
        </LinearLayout>
    </TabHost>

    <View
        app:layout_alwaysShow="true"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/colorBackground"
        android:foreground="?android:attr/dividerVertical" />
</com.android.intentresolver.widget.ResolverDrawerLayout>
