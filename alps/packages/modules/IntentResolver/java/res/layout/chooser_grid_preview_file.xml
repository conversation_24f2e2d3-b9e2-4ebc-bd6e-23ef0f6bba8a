<?xml version="1.0" encoding="utf-8"?>
<!--
/*
* Copyright 2019, The Android Open Source Project
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/
-->
<!-- Layout Option: File preview, icon, filename, copy-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    android:id="@androidprv:id/content_preview_file_area"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?androidprv:attr/materialColorSurfaceContainer">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="@dimen/chooser_edge_margin_normal"
        android:layout_marginBottom="8dp"
        android:padding="@dimen/chooser_edge_margin_normal"
        android:background="@drawable/chooser_content_preview_rounded"
        android:id="@androidprv:id/content_preview_file_layout">

        <ImageView
            android:id="@+id/content_preview_file_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_file_copy"
            android:scaleType="fitCenter"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/content_preview_file_icon"
            android:layout_alignParentTop="true"
            android:layout_alignWithParentIfMissing="true">

            <TextView
                android:id="@+id/content_preview_filename"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="middle"
                android:gravity="start|top"
                android:singleLine="true"
                android:textStyle="bold"
                android:textColor="?androidprv:attr/materialColorOnSurface"
                android:textSize="12sp"
                android:lineHeight="16sp"
                android:textAppearance="@style/TextAppearance.ChooserDefault"/>

            <TextView
                android:id="@+id/content_preview_more_files"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start|top"
                android:singleLine="true"
                android:textColor="?androidprv:attr/materialColorOnSurfaceVariant"
                android:textSize="12sp"
                android:lineHeight="16sp"
                android:textAppearance="@style/TextAppearance.ChooserDefault"/>

        </LinearLayout>
    </RelativeLayout>

    <include layout="@layout/chooser_action_row"/>

</LinearLayout>

