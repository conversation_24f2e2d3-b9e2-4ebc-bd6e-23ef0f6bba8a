<?xml version="1.0" encoding="utf-8"?>
<!--
/*
* Copyright 2019, The Android Open Source Project
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?androidprv:attr/materialColorSurfaceContainer">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_horizontal"
        android:layout_marginBottom="8dp"
        android:layout_marginHorizontal="@dimen/chooser_edge_margin_normal"
        android:padding="@dimen/chooser_edge_margin_normal_half"
        android:background="@drawable/chooser_content_preview_rounded">

        <com.android.intentresolver.widget.RoundedRectImageView
            android:id="@+id/image_view"
            android:layout_width="@dimen/width_text_image_preview_size"
            android:layout_height="@dimen/width_text_image_preview_size"
            android:scaleType="centerCrop"
            app:radius="@dimen/chooser_corner_radius_small"
        />

        <TextView
            android:id="@+id/content_preview_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/chooser_edge_margin_normal_half"
            android:maxLines="@integer/text_preview_lines"
            android:ellipsize="end"
            android:linksClickable="false"
            android:textColor="?androidprv:attr/materialColorOnSurfaceVariant"
            android:textAppearance="@style/TextAppearance.ChooserDefault"/>
    </LinearLayout>

    <include layout="@layout/chooser_action_row"/>
</LinearLayout>
