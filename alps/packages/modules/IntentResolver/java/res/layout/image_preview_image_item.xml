<!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!-- Note that this is the layout for landscape phones, the default is in drawable-480dp -->
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/chooser_preview_image_height_tall"
    android:layout_height="@dimen/chooser_preview_image_height_tall">

    <com.android.intentresolver.widget.RoundedRectImageView
            android:id="@+id/image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentTop="true"
            android:adjustViewBounds="false"
            android:scaleType="centerCrop"
            app:radius="8dp" />

    <FrameLayout
        android:id="@+id/badge_frame"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="@+id/image"
        app:layout_constraintEnd_toEndOf="@+id/image"
        app:layout_constraintTop_toTopOf="@+id/image"
        app:layout_constraintBottom_toBottomOf="@+id/image"
        android:background="@drawable/content_preview_badge_bg">

        <ImageView
            android:id="@+id/badge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="center"
            android:tint="@android:color/white"
            android:layout_gravity="center" />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
