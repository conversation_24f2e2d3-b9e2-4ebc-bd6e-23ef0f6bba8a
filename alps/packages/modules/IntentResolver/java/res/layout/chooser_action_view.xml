<!--
  ~ Copyright (C) 2019 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License
  -->

<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    style="?android:attr/borderlessButtonStyle"
    android:background="@drawable/chooser_action_button_bg"
    android:paddingVertical="15dp"
    android:paddingHorizontal="@dimen/chooser_edge_margin_normal_half"
    android:clickable="true"
    android:drawablePadding="6dp"
    android:drawableTint="?androidprv:attr/materialColorOnSurface"
    android:drawableTintMode="src_in"
    android:ellipsize="end"
    android:gravity="center"
    android:maxLines="1"
    android:textColor="?androidprv:attr/materialColorOnSurface"
    android:textSize="@dimen/chooser_action_view_text_size" />
