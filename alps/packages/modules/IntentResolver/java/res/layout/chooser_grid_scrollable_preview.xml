<?xml version="1.0" encoding="utf-8"?>
<!--
/*
* Copyright 2015, The Android Open Source Project
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/
-->
<com.android.intentresolver.widget.ResolverDrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    app:maxCollapsedHeight="0dp"
    app:maxCollapsedHeightSmall="56dp"
    app:useScrollablePreviewNestedFlingLogic="true"
    android:maxWidth="@dimen/chooser_width"
    android:id="@androidprv:id/contentPanel">

    <RelativeLayout
        android:id="@androidprv:id/chooser_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_alwaysShow="true"
        android:elevation="0dp"
        android:background="@drawable/bottomsheet_background">

        <View
            android:id="@androidprv:id/drag"
            android:layout_width="64dp"
            android:layout_height="4dp"
            android:background="@drawable/ic_drag_handle"
            android:layout_marginTop="@dimen/chooser_edge_margin_thin"
            android:layout_marginBottom="@dimen/chooser_edge_margin_thin"
            android:layout_centerHorizontal="true"
            android:layout_alignParentTop="true" />

        <TextView android:id="@android:id/title"
                  android:layout_height="wrap_content"
                  android:layout_width="wrap_content"
                  android:textAppearance="@android:style/TextAppearance.DeviceDefault.WindowTitle"
                  android:gravity="center"
                  android:paddingBottom="@dimen/chooser_view_spacing"
                  android:paddingLeft="24dp"
                  android:paddingRight="24dp"
                  android:visibility="gone"
                  android:layout_below="@androidprv:id/drag"
                  android:layout_centerHorizontal="true"/>
    </RelativeLayout>

    <FrameLayout
        android:id="@+id/chooser_headline_row_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_alwaysShow="true"
        android:background="?androidprv:attr/materialColorSurfaceContainer">

        <ViewStub
            android:id="@+id/chooser_headline_row_stub"
            android:inflatedId="@+id/chooser_headline_row"
            android:layout="@layout/chooser_headline_row"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/chooser_edge_margin_normal"
            android:layout_marginBottom="@dimen/chooser_view_spacing" />
    </FrameLayout>

    <com.android.intentresolver.widget.ChooserNestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <FrameLayout
                android:id="@androidprv:id/content_preview_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone" />

            <TabHost
                android:id="@androidprv:id/profile_tabhost"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:background="?androidprv:attr/materialColorSurfaceContainer">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TabWidget
                        android:id="@android:id/tabs"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone">
                    </TabWidget>
                    <FrameLayout
                        android:id="@android:id/tabcontent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <com.android.intentresolver.ResolverViewPager
                            android:id="@androidprv:id/profile_pager"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                    </FrameLayout>
                </LinearLayout>
            </TabHost>
    </LinearLayout>

    </com.android.intentresolver.widget.ChooserNestedScrollView>

</com.android.intentresolver.widget.ResolverDrawerLayout>
