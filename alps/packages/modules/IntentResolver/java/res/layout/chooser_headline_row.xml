<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/chooser_edge_margin_normal"
    android:layout_marginBottom="@dimen/chooser_view_spacing"
>
    <TextView
        android:id="@+id/headline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/barrier"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constrainedWidth="true"
        style="@style/TextAppearance.ChooserDefault"
        android:fontFamily="@androidprv:string/config_headlineFontFamily"
        android:textSize="@dimen/chooser_headline_text_size"
        />

    <TextView
        android:id="@+id/metadata"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/barrier"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constrainedWidth="true"
        app:layout_constraintTop_toBottomOf="@id/headline"
        style="@style/TextAppearance.ChooserDefault"
        android:fontFamily="@androidprv:string/config_bodyFontFamily"
        android:textSize="12sp"
        />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="start"
        app:constraint_referenced_ids="reselection_action,include_text_action" />

    <TextView
        android:id="@+id/reselection_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="@dimen/modify_share_text_toggle_max_width"
        android:background="@drawable/chooser_action_button_bg"
        app:layout_constraintEnd_toEndOf="parent"
        android:maxLines="2"
        android:ellipsize="end"
        android:visibility="gone"
        android:paddingVertical="3dp"
        android:paddingHorizontal="@dimen/chooser_edge_margin_normal_half"
        style="?android:attr/borderlessButtonStyle"
        android:drawableEnd="@drawable/chevron_right"
        android:textColor="?androidprv:attr/materialColorOnSurface"
        android:textSize="12sp"
        />

    <!-- This is only relevant for image+text preview, but needs to be in this layout so it can
         stay at the top if there's no reselection action. -->
    <CheckBox
        android:id="@+id/include_text_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="@dimen/modify_share_text_toggle_max_width"
        android:layout_marginTop="16dp"
        app:layout_goneMarginTop="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/reselection_action"
        android:layout_alignWithParentIfMissing="true"
        android:textColor="?androidprv:attr/materialColorOnSurface"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>
