<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2022 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<com.android.intentresolver.widget.ResolverDrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:maxWidth="@dimen/resolver_max_width"
    app:maxCollapsedHeight="@dimen/resolver_max_collapsed_height"
    app:maxCollapsedHeightSmall="56dp"
    android:id="@androidprv:id/contentPanel">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_alwaysShow="true"
        android:elevation="@dimen/resolver_elevation"
        android:paddingTop="24dp"
        android:paddingStart="@dimen/resolver_edge_margin"
        android:paddingEnd="@dimen/resolver_edge_margin"
        android:paddingBottom="@dimen/resolver_title_padding_bottom"
        android:background="@drawable/bottomsheet_background">

        <ImageView
            android:id="@android:id/icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:scaleType="fitCenter"
        />

        <TextView
            android:id="@androidprv:id/open_cross_profile"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="16dp"
            android:layout_below="@android:id/icon"
            android:layout_centerHorizontal="true"
            android:textSize="24sp"
            android:lineHeight="32sp"
            android:gravity="center"
            android:textColor="?android:textColorPrimary"
        />
    </RelativeLayout>

    <LinearLayout
        android:id="@androidprv:id/button_bar_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_alwaysShow="true"
        android:paddingTop="32dp"
        android:paddingBottom="@dimen/resolver_button_bar_spacing"
        android:orientation="vertical"
        android:background="?android:attr/colorBackground"
        app:layout_ignoreOffset="true">
        <RelativeLayout
            style="?android:attr/buttonBarStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_ignoreOffset="true"
            app:layout_hasNestedScrollIndicator="true"
            android:gravity="end|center_vertical"
            android:orientation="horizontal"
            android:layoutDirection="locale"
            android:measureWithLargestChild="true"
            android:paddingHorizontal="16dp"
            android:paddingBottom="2dp"
            android:elevation="@dimen/resolver_elevation">

            <Button
                android:id="@androidprv:id/use_same_profile_browser"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:maxLines="2"
                android:background="@drawable/resolver_outlined_button_bg"
                style="?android:attr/borderlessButtonStyle"
                android:paddingHorizontal="16dp"
                android:fontFamily="@androidprv:string/config_headlineFontFamilyMedium"
                android:textAllCaps="false"
                android:text="@string/activity_resolver_use_once"
            />

            <Button
                android:id="@androidprv:id/button_open"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:maxLines="2"
                android:paddingHorizontal="16dp"
                android:background="@drawable/resolver_button_bg"
                style="?android:attr/borderlessButtonStyle"
                android:fontFamily="@androidprv:string/config_headlineFontFamilyMedium"
                android:textAllCaps="false"
                android:textColor="@androidprv:color/resolver_button_text"
                android:text="@string/whichViewApplicationLabel"
            />
        </RelativeLayout>
    </LinearLayout>
</com.android.intentresolver.widget.ResolverDrawerLayout>
