<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2006, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
              android:id="@androidprv:id/item"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:minHeight="100dp"
              android:gravity="top|center_horizontal"
              android:paddingVertical="@dimen/grid_padding"
              android:paddingHorizontal="4dp"
              android:focusable="true"
              android:background="?android:attr/selectableItemBackgroundBorderless">

    <ImageView android:id="@android:id/icon"
               android:layout_width="@dimen/chooser_icon_size"
               android:layout_height="@dimen/chooser_icon_size"
               android:layout_marginHorizontal="8dp"
               android:scaleType="fitCenter" />

    <!-- Size manually tuned to match specs -->
    <Space android:layout_width="1dp"
           android:layout_height="7dp"/>

    <!-- NOTE: for id/text1 and id/text2 below set the width to match parent as a workaround for
         b/269395540 i.e. prevent views bounds change during a transition animation. It does not
         affect pinned views as we change their layout parameters programmatically (but that's even
         more narrow possibility and it's not clear if the root cause or the bug would affect it).
    -->
    <!-- App name or Direct Share target name, DS set to 2 lines -->
    <com.android.intentresolver.widget.BadgeTextView
              android:id="@android:id/text1"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:textAppearance="?android:attr/textAppearanceSmall"
              android:textColor="?androidprv:attr/materialColorOnSurface"
              android:textSize="@dimen/chooser_grid_target_name_text_size"
              android:maxLines="1"
              android:ellipsize="end" />

    <!-- Activity name if set, gone for Direct Share targets -->
    <TextView android:id="@android:id/text2"
              android:textAppearance="?android:attr/textAppearanceSmall"
              android:textSize="@dimen/chooser_grid_activity_name_text_size"
              android:textColor="?androidprv:attr/materialColorOnSurfaceVariant"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:lines="1"
              android:gravity="top|center_horizontal"
              android:ellipsize="end"/>

</LinearLayout>

