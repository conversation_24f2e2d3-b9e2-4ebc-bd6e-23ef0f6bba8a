<?xml version="1.0" encoding="utf-8"?>
<!--
/* //device/apps/common/res/any/layout/resolve_list_item.xml
**
** Copyright 2006, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
              android:orientation="horizontal"
              android:layout_height="wrap_content"
              android:layout_width="match_parent"
              android:minHeight="?android:attr/listPreferredItemHeightSmall"
              android:background="?android:attr/activatedBackgroundIndicator">

    <!-- Activity icon when presenting dialog
         Size will be filled in by ResolverActivity -->
    <ImageView android:id="@android:id/icon"
               android:layout_width="@dimen/resolver_icon_size"
               android:layout_height="@dimen/resolver_icon_size"
               android:layout_gravity="start|center_vertical"
               android:layout_marginStart="@dimen/resolver_icon_margin"
               android:layout_marginEnd="@dimen/resolver_icon_margin"
               android:layout_marginTop="12dp"
               android:layout_marginBottom="12dp"
               android:scaleType="fitCenter" />

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:gravity="start|center_vertical"
              android:orientation="vertical"
              android:paddingEnd="@dimen/resolver_edge_margin"
              android:layout_height="wrap_content"
              android:layout_width="wrap_content"
              android:layout_gravity="start|center_vertical">
        <!-- Activity name -->
        <TextView android:id="@android:id/text1"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:layout_gravity="start|center_vertical"
                  android:textColor="?android:attr/textColorPrimary"
                  android:fontFamily="@androidprv:string/config_bodyFontFamily"
                  android:textSize="16sp"
                  android:minLines="1"
                  android:maxLines="1"
                  android:ellipsize="marquee" />
        <!-- Extended activity info to distinguish between duplicate activity names
            or provide record w/o permission warnings.
        -->
        <TextView android:id="@android:id/text2"
                  android:textColor="?android:attr/textColorSecondary"
                  android:fontFamily="@androidprv:string/config_bodyFontFamily"
                  android:layout_gravity="start|center_vertical"
                  android:textSize="14sp"
                  android:visibility="gone"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:minLines="1"
                  android:maxLines="2"
                  android:ellipsize="marquee" />
    </LinearLayout>
</LinearLayout>

