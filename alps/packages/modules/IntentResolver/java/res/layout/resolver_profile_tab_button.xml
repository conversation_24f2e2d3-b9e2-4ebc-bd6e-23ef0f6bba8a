<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2022 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

    <Button
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_weight="1"
        android:layout_marginHorizontal="@dimen/resolver_profile_tab_margin"
        android:background="@drawable/inset_resolver_profile_tab_bg"
        android:textColor="@color/resolver_profile_tab_text"
        android:textSize="@dimen/resolver_tab_text_size"
        android:textAppearance="@android:style/TextAppearance.DeviceDefault.DialogWindowTitle"
        style="?android:attr/borderlessButtonStyle" />
