<!--
  ~ Copyright (C) 2019 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="blocksDescendants">
    <!--    ^^^ Block descendants from receiving focus to prevent NestedScrollView
    (ChooserNestedScrollView) scrolling to the focused view when switching tabs. Without it, TabHost
    view will request focus on the newly activated tab. The RecyclerView from this layout gets
    focused and  notifies its parents (including NestedScrollView) about it through
    #requestChildFocus method call. NestedScrollView's view implementation of the method  will
    scroll to the focused view. -->

    <androidx.recyclerview.widget.RecyclerView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layoutManager="com.android.intentresolver.ChooserGridLayoutManager"
        android:id="@androidprv:id/resolver_list"
        android:clipToPadding="false"
        android:background="?androidprv:attr/materialColorSurfaceContainer"
        android:scrollbars="none"
        android:nestedScrollingEnabled="true" />

    <include layout="@layout/resolver_empty_states" />
</RelativeLayout>
