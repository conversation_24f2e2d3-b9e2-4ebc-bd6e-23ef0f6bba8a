<?xml version="1.0" encoding="utf-8"?>
<!--
/*
 * Copyright 2014, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
<TextView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_alwaysShow="true"
    android:text="@string/use_a_different_app"
    android:textColor="?android:attr/textColorPrimary"
    android:fontFamily="@androidprv:string/config_headlineFontFamilyMedium"
    android:textSize="16sp"
    android:gravity="start|center_vertical"
    android:paddingStart="@dimen/resolver_edge_margin"
    android:paddingEnd="@dimen/resolver_edge_margin"
    android:paddingTop="@dimen/resolver_small_margin"
    android:paddingBottom="@dimen/resolver_edge_margin"
    android:elevation="1dp" />
