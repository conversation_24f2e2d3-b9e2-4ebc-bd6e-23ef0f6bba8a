/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.intentresolver.contentpreview.payloadtoggle.domain.model

import com.android.intentresolver.icon.ComposeIcon

/** An action that the user can take, provided by the sharing application. */
data class ActionModel(
    /** Text shown for this action in the UI. */
    val label: CharSequence,
    /** An optional [ComposeIcon] that will be displayed in the UI with this action. */
    val icon: ComposeIcon?,
    /**
     * Performs the action. The argument indicates the index in the UI that this action is shown.
     */
    val performAction: (index: Int) -> Unit,
)
