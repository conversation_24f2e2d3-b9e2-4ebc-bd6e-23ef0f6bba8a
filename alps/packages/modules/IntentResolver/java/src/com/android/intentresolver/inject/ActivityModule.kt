/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.intentresolver.inject

import android.app.Activity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent
import kotlinx.coroutines.CoroutineScope

@Module
@InstallIn(ActivityComponent::class)
object ActivityModule {

    @Provides
    @ActivityOwned
    fun lifecycle(activity: Activity): Lifecycle {
        check(activity is LifecycleOwner) { "activity must implement LifecycleOwner" }
        return activity.lifecycle
    }

    @Provides
    @ActivityOwned
    fun activityScope(activity: Activity): CoroutineScope {
        check(activity is LifecycleOwner) { "activity must implement LifecycleOwner" }
        return activity.lifecycleScope
    }
}
