/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.android.intentresolver.emptystate;

import android.annotation.Nullable;

import com.android.intentresolver.ResolverListAdapter;

/**
 * Returns an empty state to show for the current profile page (tab) if necessary.
 * This could be used e.g. to show a blocker on a tab if device management policy doesn't
 * allow to use it or there are no apps available.
 */
public interface EmptyStateProvider {
    /**
     * When a non-null empty state is returned the corresponding profile page will show
     * this empty state
     * @param resolverListAdapter the current adapter
     */
    @Nullable
    default EmptyState getEmptyState(ResolverListAdapter resolverListAdapter) {
        return null;
    }
}
