/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.intentresolver.widget

import android.view.View
import androidx.recyclerview.widget.RecyclerView

internal val RecyclerView.areAllChildrenVisible: Boolean
    get() {
        val count = getChildCount()
        if (count == 0) return true
        val first = getChildAt(0)
        val last = getChildAt(count - 1)
        val itemCount = adapter?.itemCount ?: 0
        return getChildAdapterPosition(first) == 0 &&
            getChildAdapterPosition(last) == itemCount - 1 &&
            isFullyVisible(first) &&
            isFullyVisible(last)
    }

private fun RecyclerView.isFullyVisible(view: View): Boolean =
    view.left >= paddingLeft && view.right <= width - paddingRight
