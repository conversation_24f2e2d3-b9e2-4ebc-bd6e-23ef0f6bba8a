/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.intentresolver.annotation

/**
 * Apply to code which exists specifically to easy integration with existing Java and Java APIs.
 *
 * The goal is to prevent usage from <PERSON><PERSON><PERSON> when a more idiomatic alternative is available.
 */
@RequiresOptIn(
    "This is a a property, function or class specifically supporting Java " +
        "interoperability. Usage from Kotlin should be limited to interactions with Java."
)
annotation class JavaInterop
