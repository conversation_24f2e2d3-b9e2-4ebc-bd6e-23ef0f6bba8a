/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.intentresolver.contentpreview

import android.content.res.Resources
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup

internal class NoContextPreviewUi(private val type: Int) : ContentPreviewUi() {
    override fun getType(): Int = type

    override fun display(
        resources: Resources?,
        layoutInflater: LayoutInflater?,
        parent: ViewGroup?,
        headlineViewParent: View,
    ): ViewGroup? {
        Log.e(TAG, "Unexpected content preview type: $type")
        return null
    }
}
