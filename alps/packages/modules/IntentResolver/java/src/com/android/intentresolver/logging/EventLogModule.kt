/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.android.intentresolver.logging

import com.android.internal.logging.InstanceId
import com.android.internal.logging.InstanceIdSequence
import com.android.internal.logging.MetricsLogger
import com.android.internal.logging.UiEventLogger
import com.android.internal.logging.UiEventLoggerImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityRetainedComponent
import dagger.hilt.android.scopes.ActivityRetainedScoped

@Module
@InstallIn(ActivityRetainedComponent::class)
interface EventLogModule {

    @Binds @ActivityRetainedScoped fun eventLog(value: EventLogImpl): EventLog

    companion object {
        @Provides
        fun instanceId(sequence: InstanceIdSequence): InstanceId = sequence.newInstanceId()

        @Provides fun uiEventLogger(): UiEventLogger = UiEventLoggerImpl()

        @Provides fun frameworkLogger(): FrameworkStatsLogger = object : FrameworkStatsLogger {}

        @Provides fun metricsLogger(): MetricsLogger = MetricsLogger()
    }
}
