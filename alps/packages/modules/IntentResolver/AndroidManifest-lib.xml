<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2023 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.android.intentresolver" >
    <uses-permission android:name="android.permission.ACCESS_SHORTCUTS" />
    <uses-permission android:name="android.permission.BIND_RESOLVER_RANKER_SERVICE" />
    <uses-permission android:name="android.permission.GET_ANY_PROVIDER_TYPE" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
    <uses-permission android:name="android.permission.MANAGE_APP_PREDICTIONS" />
    <uses-permission android:name="android.permission.MANAGE_USERS" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.READ_DEVICE_CONFIG" />
    <uses-permission android:name="android.permission.SET_CLIP_SOURCE" />
    <uses-permission android:name="android.permission.START_ACTIVITY_AS_CALLER" />
    <uses-permission android:name="android.permission.UNLIMITED_SHORTCUTS_API_CALLS" />
    <uses-permission android:name="android.permission.QUERY_CLONED_APPS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REPORT_USAGE_STATS" />
    <uses-permission android:name="android.permission.LOG_COMPAT_CHANGE" />
    <uses-permission android:name="android.permission.READ_COMPAT_CHANGE_CONFIG" />
</manifest>
