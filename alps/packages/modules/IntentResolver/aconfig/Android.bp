//
// Copyright (C) 2023 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

aconfig_declarations {
    name: "IntentResolverFlags",
    package: "com.android.intentresolver",
    container: "system",
    srcs: ["FeatureFlags.aconfig"],
}

java_aconfig_library {
    name: "IntentResolverFlagsLib",
    aconfig_declarations: "IntentResolverFlags",
}
